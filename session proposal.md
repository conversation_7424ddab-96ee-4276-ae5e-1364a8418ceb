# Prijedlog predavanja

## Predavanje 1

**Naslov.** Šest metoda traži igrača: Umjetna inteligencija kao igrač

**Opis i ciljevi.**

Ovo predavanje s praktičnim primjerima govori o pristupima korištenju metoda umjetne inteligencije za igranje videoigara. Sadr<PERSON>aj se kreće od upoznavanja s konceptom automatiziranog igranja videoigara, preko diskusije o ulozi i korištenju modeliranja igrača pomoću metoda umjetne inteligencije, do korištenja metoda umjetne inteligencije za automatizirano igranje videoigara.

Planirani ishodi učenja:

- Objasniti koncept automatiziranog igranja videoigara i njegove primjene.
- Analizirati ulogu i primjenu modeliranja igrača pomoću metoda umjetne inteligencije u videoigrama.
- Procijeniti prikladne metode umjetne inteligencije za automatizirano igranje jednostavnih videoigara.

- Demonstrate an understanding of the facts by explaining ideas or concepts.
- Examine and break information into parts to explore relationships.
- Defend opinions and decisions; justify a course of action by making judgements about information.

## Predavanje 2

**Naslov.** 1001 kombinacija: Umjetna inteligencija u razvoju videoigara

**Opis i ciljevi.**

Sadržaj ovog predavanja s praktičnim primjerima obuhvaća odabrane metode umjetne inteligencije i načine njihovog korištenja za generiranje, razvoj ili implementaciju pojedinih dijelova videoigara. Od programskog koda, preko resursa poput zvuka ili grafika, do cijelih razina, metode umjetne inteligencije moguće je koristiti u procesu razvoja videoigara u različite svrhe.

Planirani ishodi učenja:

- Objasniti ulogu metoda umjetne inteligencije u razvoju videoigara.
- Razlikovati načine korištenja metoda umjetne inteligencije u razvoju videoigara.
- Opravdati korištenje metoda umjetne inteligencije u procesu razvoja videoigara.

- Demonstrate an understanding of the facts by explaining ideas or concepts.
- Examine and break information into parts to explore relationships.
- Defend opinions and decisions; justify a course of action by making judgements about information.
