extends Node2D

@onready var grid: Grid
@onready var character: Character
@onready var path_visualizer: PathVisualizer
@onready var ui: UI

var current_algorithm: String = "A*"
var character_start_pos: Vector2i = Vector2i(2, 2)

func _ready():
	setup_scene()

func setup_scene():
	# Create grid
	grid = Grid.new()
	add_child(grid)
	grid.cell_clicked.connect(_on_grid_clicked)
	
	# Create path visualizer
	path_visualizer = PathVisualizer.new()
	path_visualizer.initialize(grid)
	add_child(path_visualizer)
	
	# Create character
	character = Character.new()
	character.initialize(grid, character_start_pos)
	character.movement_finished.connect(_on_character_movement_finished)
	add_child(character)
	
	# Create UI
	ui = UI.new()
	ui.algorithm_changed.connect(_on_algorithm_changed)
	ui.reset_requested.connect(_on_reset_requested)
	add_child(ui)
	
	# Update initial info
	update_info_text()

func _on_grid_clicked(grid_pos: Vector2i):
	if character.is_currently_moving():
		ui.update_info("Character is still moving, please wait...")
		return
	
	var start_pos = character.get_current_position()
	
	if grid_pos == start_pos:
		ui.update_info("Character is already at that position!")
		return
	
	# Set target visualization
	path_visualizer.set_target(grid_pos)
	
	# Calculate path based on selected algorithm
	var path: Array[Vector2i] = []
	var start_time = Time.get_ticks_msec()
	
	match current_algorithm:
		"A*":
			path = AStar.find_path(grid, start_pos, grid_pos)
		"MCTS":
			path = MCTS.find_path(grid, start_pos, grid_pos, 1000)
	
	var end_time = Time.get_ticks_msec()
	var calculation_time = end_time - start_time
	
	if path.is_empty():
		ui.update_info("No path found to destination!")
		path_visualizer.clear_path()
		return
	
	# Visualize and execute path
	path_visualizer.set_path(path)
	character.move_along_path(path)
	
	# Update info with path details
	var info_text = "%s found path with %d steps (calculated in %d ms)" % [current_algorithm, path.size(), calculation_time]
	ui.update_info(info_text)

func _on_character_movement_finished():
	ui.update_info("Movement complete! Click on the grid to set a new destination.")

func _on_algorithm_changed(algorithm_name: String):
	current_algorithm = algorithm_name
	path_visualizer.clear_path()
	update_info_text()

func _on_reset_requested():
	# Reset character position
	character.initialize(grid, character_start_pos)
	path_visualizer.clear_path()
	update_info_text()

func update_info_text():
	var algorithm_info = ""
	match current_algorithm:
		"A*":
			algorithm_info = "A* - Optimal pathfinding using heuristic search"
		"MCTS":
			algorithm_info = "MCTS - Monte Carlo Tree Search pathfinding"
	
	ui.update_info("%s selected. Click on the grid to set a destination." % algorithm_info)
