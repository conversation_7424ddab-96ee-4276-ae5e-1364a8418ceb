extends Node2D
class_name PathVisualizer

var grid: Grid
var current_path: Array[Vector2i] = []
var target_position: Vector2i = Vector2i(-1, -1)

func initialize(grid_ref: Grid):
	grid = grid_ref

func set_path(path: Array[Vector2i]):
	current_path = path
	queue_redraw()

func set_target(target: Vector2i):
	target_position = target
	queue_redraw()

func clear_path():
	current_path.clear()
	target_position = Vector2i(-1, -1)
	queue_redraw()

func _draw():
	if not grid:
		return
	
	# Draw target position
	if target_position != Vector2i(-1, -1):
		var target_world_pos = grid.grid_to_world(target_position)
		var target_size = Vector2(grid.CELL_SIZE * 0.6, grid.CELL_SIZE * 0.6)
		var target_rect = Rect2(target_world_pos - target_size/2, target_size)
		draw_rect(target_rect, Color.GREEN, true)
		draw_rect(target_rect, Color.DARK_GREEN, false, 2)
		
		# Draw a small cross in the center
		var cross_size = 8
		draw_line(
			target_world_pos + Vector2(-cross_size, -cross_size),
			target_world_pos + Vector2(cross_size, cross_size),
			Color.DARK_GREEN, 2
		)
		draw_line(
			target_world_pos + Vector2(-cross_size, cross_size),
			target_world_pos + Vector2(cross_size, -cross_size),
			Color.DARK_GREEN, 2
		)
	
	# Draw path
	if current_path.size() > 1:
		var path_color = Color.YELLOW
		path_color.a = 0.8
		
		# Draw path as connected lines
		for i in range(current_path.size() - 1):
			var start_world = grid.grid_to_world(current_path[i])
			var end_world = grid.grid_to_world(current_path[i + 1])
			draw_line(start_world, end_world, path_color, 4)
		
		# Draw path nodes as small circles
		var node_color = Color.ORANGE
		for i in range(1, current_path.size() - 1):  # Skip start and end
			var node_world = grid.grid_to_world(current_path[i])
			draw_circle(node_world, 6, node_color)
			draw_circle(node_world, 6, Color.DARK_ORANGE, false, 2)
		
		# Draw start position marker
		if current_path.size() > 0:
			var start_world = grid.grid_to_world(current_path[0])
			draw_circle(start_world, 8, Color.CYAN)
			draw_circle(start_world, 8, Color.DARK_CYAN, false, 2)
		
		# Draw end position marker
		if current_path.size() > 1:
			var end_world = grid.grid_to_world(current_path[-1])
			draw_circle(end_world, 8, Color.MAGENTA)
			draw_circle(end_world, 8, Color.PURPLE, false, 2)
