extends Node2D
class_name Grid

signal cell_clicked(grid_pos: Vector2i)

const CELL_SIZE = 32
const GRID_WIDTH = 25
const GRID_HEIGHT = 20

var obstacles: Array[Vector2i] = []
var grid_to_world_offset: Vector2

func _ready():
	# Center the grid on screen
	var screen_size = get_viewport().get_visible_rect().size
	grid_to_world_offset = Vector2(
		(screen_size.x - GRID_WIDTH * CELL_SIZE) / 2,
		(screen_size.y - GRID_HEIGHT * CELL_SIZE) / 2 + 50  # Leave space for UI
	)
	
	# Create some obstacles for demonstration
	create_demo_obstacles()

func create_demo_obstacles():
	# Create a simple maze-like pattern
	obstacles.clear()
	
	# Vertical walls
	for y in range(5, 15):
		obstacles.append(Vector2i(8, y))
		obstacles.append(Vector2i(16, y))
	
	# Horizontal walls
	for x in range(3, 8):
		obstacles.append(Vector2i(x, 10))
	for x in range(17, 22):
		obstacles.append(Vector2i(x, 10))
	
	# Some scattered obstacles
	obstacles.append_array([
		Vector2i(5, 5), Vector2i(6, 5), Vector2i(7, 5),
		Vector2i(18, 5), Vector2i(19, 5), Vector2i(20, 5),
		Vector2i(12, 3), Vector2i(12, 4), Vector2i(12, 5),
		Vector2i(12, 15), Vector2i(12, 16), Vector2i(12, 17)
	])

func _draw():
	# Draw grid lines
	var grid_color = Color.GRAY
	grid_color.a = 0.3
	
	# Vertical lines
	for x in range(GRID_WIDTH + 1):
		var start_pos = grid_to_world_offset + Vector2(x * CELL_SIZE, 0)
		var end_pos = grid_to_world_offset + Vector2(x * CELL_SIZE, GRID_HEIGHT * CELL_SIZE)
		draw_line(start_pos, end_pos, grid_color, 1)
	
	# Horizontal lines
	for y in range(GRID_HEIGHT + 1):
		var start_pos = grid_to_world_offset + Vector2(0, y * CELL_SIZE)
		var end_pos = grid_to_world_offset + Vector2(GRID_WIDTH * CELL_SIZE, y * CELL_SIZE)
		draw_line(start_pos, end_pos, grid_color, 1)
	
	# Draw obstacles
	for obstacle in obstacles:
		var rect = Rect2(
			grid_to_world_offset + Vector2(obstacle.x * CELL_SIZE, obstacle.y * CELL_SIZE),
			Vector2(CELL_SIZE, CELL_SIZE)
		)
		draw_rect(rect, Color.RED, true)

func _input(event):
	if event is InputEventMouseButton and event.pressed and event.button_index == MOUSE_BUTTON_LEFT:
		var grid_pos = world_to_grid(event.position)
		if is_valid_position(grid_pos) and not is_obstacle(grid_pos):
			cell_clicked.emit(grid_pos)

func world_to_grid(world_pos: Vector2) -> Vector2i:
	var local_pos = world_pos - grid_to_world_offset
	return Vector2i(int(local_pos.x / CELL_SIZE), int(local_pos.y / CELL_SIZE))

func grid_to_world(grid_pos: Vector2i) -> Vector2:
	return grid_to_world_offset + Vector2(grid_pos.x * CELL_SIZE + CELL_SIZE/2, grid_pos.y * CELL_SIZE + CELL_SIZE/2)

func is_valid_position(pos: Vector2i) -> bool:
	return pos.x >= 0 and pos.x < GRID_WIDTH and pos.y >= 0 and pos.y < GRID_HEIGHT

func is_obstacle(pos: Vector2i) -> bool:
	return pos in obstacles

func get_neighbors(pos: Vector2i) -> Array[Vector2i]:
	var neighbors: Array[Vector2i] = []
	var directions = [Vector2i(0, 1), Vector2i(1, 0), Vector2i(0, -1), Vector2i(-1, 0)]
	
	for direction in directions:
		var neighbor = pos + direction
		if is_valid_position(neighbor) and not is_obstacle(neighbor):
			neighbors.append(neighbor)
	
	return neighbors
