# Pathfinding Demo Game

A simple Godot 4.4 demonstration game showcasing two different pathfinding algorithms: A* and Monte Carlo Tree Search (MCTS).

## Features

- **Grid-based environment** with obstacles
- **Two pathfinding algorithms**:
  - **A*** - Optimal pathfinding using heuristic search
  - **MCTS** - Monte Carlo Tree Search pathfinding
- **Interactive character** (blue box) that moves along calculated paths
- **Visual path representation** with colored lines and markers
- **Real-time algorithm switching**
- **Performance metrics** showing calculation time and path length

## How to Use

1. **Open the project** in Godot 4.4
2. **Run the scene** (Main.tscn is set as the main scene)
3. **Select an algorithm** using the dropdown menu at the top
4. **Click anywhere on the grid** to set a destination
5. **Watch the character** move along the calculated path
6. **Use the Reset button** to return the character to the starting position

## Visual Elements

- **Blue box** - The character/agent
- **Red squares** - Obstacles that block movement
- **Green square with cross** - Current destination target
- **Yellow line** - The calculated path
- **Orange circles** - Path waypoints
- **<PERSON>an circle** - Path start point
- **Magenta circle** - Path end point

## Algorithm Comparison

### A* (A-Star)
- **Optimal**: Always finds the shortest path
- **Fast**: Efficient for most scenarios
- **Deterministic**: Same input always produces same output
- **Heuristic-based**: Uses Manhattan distance to guide search

### MCTS (Monte Carlo Tree Search)
- **Probabilistic**: Uses random sampling and tree search
- **Adaptive**: Can handle complex scenarios
- **Non-deterministic**: May produce different paths for same input
- **Exploration-based**: Balances exploration and exploitation

## Educational Value

This demo is perfect for teaching:
- Basic pathfinding concepts
- Algorithm comparison and trade-offs
- Grid-based navigation
- Heuristic search vs. probabilistic methods
- Real-time algorithm performance analysis

## Technical Details

- **Grid size**: 25x20 cells
- **Cell size**: 32x32 pixels
- **MCTS iterations**: 1000 per pathfinding request
- **Movement speed**: 200 pixels/second
- **Supported input**: Mouse clicks for destination selection

## Files Structure

- `Main.gd` - Main game controller
- `Grid.gd` - Grid system and obstacle management
- `AStar.gd` - A* pathfinding implementation
- `MCTS.gd` - Monte Carlo Tree Search implementation
- `Character.gd` - Character movement and animation
- `PathVisualizer.gd` - Path and target visualization
- `UI.gd` - User interface and controls
- `Main.tscn` - Main scene file
- `project.godot` - Godot project configuration
