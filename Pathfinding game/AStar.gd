extends RefCounted
class_name AStar

class AStarNode:
	var position: Vector2i
	var g_cost: float = 0.0  # Distance from start
	var h_cost: float = 0.0  # Heuristic distance to goal
	var f_cost: float = 0.0  # Total cost (g + h)
	var parent: AStarNode = null
	
	func _init(pos: Vector2i):
		position = pos
	
	func calculate_f_cost():
		f_cost = g_cost + h_cost

static func find_path(grid: Grid, start: Vector2i, goal: Vector2i) -> Array[Vector2i]:
	if not grid.is_valid_position(start) or not grid.is_valid_position(goal):
		return []
	
	if grid.is_obstacle(start) or grid.is_obstacle(goal):
		return []
	
	if start == goal:
		return [start]
	
	var open_set: Array[AStarNode] = []
	var closed_set: Dictionary = {}
	var all_nodes: Dictionary = {}
	
	# Create start node
	var start_node = AStarNode.new(start)
	start_node.g_cost = 0
	start_node.h_cost = heuristic(start, goal)
	start_node.calculate_f_cost()
	
	open_set.append(start_node)
	all_nodes[start] = start_node
	
	while open_set.size() > 0:
		# Find node with lowest f_cost
		var current_node = open_set[0]
		var current_index = 0
		
		for i in range(1, open_set.size()):
			if open_set[i].f_cost < current_node.f_cost:
				current_node = open_set[i]
				current_index = i
		
		# Move current node from open to closed set
		open_set.remove_at(current_index)
		closed_set[current_node.position] = current_node
		
		# Check if we reached the goal
		if current_node.position == goal:
			return reconstruct_path(current_node)
		
		# Check all neighbors
		var neighbors = grid.get_neighbors(current_node.position)
		for neighbor_pos in neighbors:
			if neighbor_pos in closed_set:
				continue
			
			var tentative_g_cost = current_node.g_cost + 1.0
			
			var neighbor_node: AStarNode
			if neighbor_pos in all_nodes:
				neighbor_node = all_nodes[neighbor_pos]
			else:
				neighbor_node = AStarNode.new(neighbor_pos)
				all_nodes[neighbor_pos] = neighbor_node
			
			# If this path to neighbor is better than any previous one
			if neighbor_node not in open_set or tentative_g_cost < neighbor_node.g_cost:
				neighbor_node.parent = current_node
				neighbor_node.g_cost = tentative_g_cost
				neighbor_node.h_cost = heuristic(neighbor_pos, goal)
				neighbor_node.calculate_f_cost()
				
				if neighbor_node not in open_set:
					open_set.append(neighbor_node)
	
	# No path found
	return []

static func heuristic(a: Vector2i, b: Vector2i) -> float:
	# Manhattan distance
	return abs(a.x - b.x) + abs(a.y - b.y)

static func reconstruct_path(node: AStarNode) -> Array[Vector2i]:
	var path: Array[Vector2i] = []
	var current = node
	
	while current != null:
		path.push_front(current.position)
		current = current.parent
	
	return path
