extends Control
class_name UI

signal algorithm_changed(algorithm_name: String)
signal reset_requested

@onready var algorithm_option: OptionButton
@onready var info_label: Label
@onready var reset_button: Button

var current_algorithm: String = "A*"

func _ready():
	create_ui_elements()

func create_ui_elements():
	# Create main container
	var main_container = VBoxContainer.new()
	main_container.set_anchors_and_offsets_preset(Control.PRESET_TOP_WIDE)
	main_container.size_flags_horizontal = Control.SIZE_EXPAND_FILL
	add_child(main_container)
	
	# Create top panel
	var top_panel = HBoxContainer.new()
	top_panel.add_theme_constant_override("separation", 20)
	main_container.add_child(top_panel)
	
	# Algorithm selection
	var algorithm_label = Label.new()
	algorithm_label.text = "Algorithm:"
	algorithm_label.add_theme_font_size_override("font_size", 16)
	top_panel.add_child(algorithm_label)
	
	algorithm_option = OptionButton.new()
	algorithm_option.add_item("A*")
	algorithm_option.add_item("MCTS")
	algorithm_option.selected = 0
	algorithm_option.add_theme_font_size_override("font_size", 16)
	algorithm_option.item_selected.connect(_on_algorithm_selected)
	top_panel.add_child(algorithm_option)
	
	# Add spacer
	var spacer = Control.new()
	spacer.size_flags_horizontal = Control.SIZE_EXPAND_FILL
	top_panel.add_child(spacer)
	
	# Reset button
	reset_button = Button.new()
	reset_button.text = "Reset"
	reset_button.add_theme_font_size_override("font_size", 16)
	reset_button.pressed.connect(_on_reset_pressed)
	top_panel.add_child(reset_button)
	
	# Info label
	info_label = Label.new()
	info_label.text = "Click on the grid to set a destination for the blue character"
	info_label.add_theme_font_size_override("font_size", 14)
	info_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	main_container.add_child(info_label)
	
	# Add some spacing
	main_container.add_theme_constant_override("separation", 10)

func _on_algorithm_selected(index: int):
	match index:
		0:
			current_algorithm = "A*"
		1:
			current_algorithm = "MCTS"
	
	algorithm_changed.emit(current_algorithm)

func _on_reset_pressed():
	reset_requested.emit()

func update_info(text: String):
	if info_label:
		info_label.text = text

func get_current_algorithm() -> String:
	return current_algorithm
