extends RefCounted
class_name MCTS

class MCTSNode:
    var position: Vector2i
    var parent: MCTSNode = null
    var children: Array[MCTSNode] = []
    var visits: int = 0
    var wins: float = 0.0
    var untried_moves: Array[Vector2i] = []

    func _init(pos: Vector2i, grid: Grid):
        position = pos
        untried_moves = grid.get_neighbors(pos)

    func is_fully_expanded() -> bool:
        return untried_moves.is_empty()

    func is_terminal(goal: Vector2i) -> bool:
        return position == goal

    func ucb1_value(exploration_param: float = 1.414) -> float:
        if visits == 0:
            return INF

        var exploitation = wins / float(visits)
        var exploration = exploration_param * sqrt(log(parent.visits) / float(visits))
        return exploitation + exploration

    func select_child() -> MCTSNode:
        var best_child: MCTSNode = null
        var best_value = -INF

        for child in children:
            var value = child.ucb1_value()
            if value > best_value:
                best_value = value
                best_child = child

        return best_child

    func expand(grid: Grid) -> MCTSNode:
        if untried_moves.is_empty():
            return null

        var move = untried_moves.pop_back()
        var child = MCTSNode.new(move, grid)
        child.parent = self
        children.append(child)
        return child

    func update(result: float):
        visits += 1
        wins += result

static func find_path(grid: Grid, start: Vector2i, goal: Vector2i, max_iterations: int = 1000) -> Array[Vector2i]:
    if not grid.is_valid_position(start) or not grid.is_valid_position(goal):
        return []

    if grid.is_obstacle(start) or grid.is_obstacle(goal):
        return []

    if start == goal:
        return [start]

    var root = MCTSNode.new(start, grid)

    for i in range(max_iterations):
        # Selection
        var node = root
        var path_to_node: Array[MCTSNode] = [node]

        while not node.is_terminal(goal) and node.is_fully_expanded() and not node.children.is_empty():
            node = node.select_child()
            path_to_node.append(node)

        # Expansion
        if not node.is_terminal(goal) and not node.is_fully_expanded():
            var new_node = node.expand(grid)
            if new_node != null:
                node = new_node
                path_to_node.append(node)

        # Simulation
        var result = simulate(grid, node.position, goal)

        # Backpropagation
        for path_node in path_to_node:
            path_node.update(result)

    # Extract best path
    return extract_best_path(root, goal)

static func simulate(grid: Grid, start: Vector2i, goal: Vector2i, max_steps: int = 200) -> float:
    var current = start
    var steps = 0
    var visited: Dictionary = {}

    while current != goal and steps < max_steps:
        if current in visited:
            visited[current] += 1
            # Penalize revisiting positions
            if visited[current] > 3:
                return 0.0
        else:
            visited[current] = 1

        var neighbors = grid.get_neighbors(current)
        if neighbors.is_empty():
            return 0.0

        # Bias towards goal
        var best_neighbor = neighbors[0]
        var best_distance = manhattan_distance(best_neighbor, goal)

        for neighbor in neighbors:
            var distance = manhattan_distance(neighbor, goal)
            # Add some randomness but prefer closer positions
            var random_factor = randf() * 0.3
            if distance + random_factor < best_distance:
                best_distance = distance
                best_neighbor = neighbor

        current = best_neighbor
        steps += 1

    if current == goal:
        # Reward finding goal, with bonus for shorter paths
        return 1.0 + (max_steps - steps) / float(max_steps)
    else:
        # Partial reward based on how close we got
        var final_distance = manhattan_distance(current, goal)
        var initial_distance = manhattan_distance(start, goal)
        return max(0.0, (initial_distance - final_distance) / float(initial_distance) * 0.5)

static func extract_best_path(root: MCTSNode, goal: Vector2i) -> Array[Vector2i]:
    var path: Array[Vector2i] = []
    var current = root

    path.append(current.position)

    while not current.is_terminal(goal) and not current.children.is_empty():
        # Select child with highest visit count (most promising)
        var best_child: MCTSNode = null
        var best_visits = -1

        for child in current.children:
            if child.visits > best_visits:
                best_visits = child.visits
                best_child = child

        if best_child == null:
            break

        current = best_child
        path.append(current.position)

        # Safety check to prevent infinite loops
        if path.size() > 100:
            break

    return path

static func manhattan_distance(a: Vector2i, b: Vector2i) -> int:
    return abs(a.x - b.x) + abs(a.y - b.y)
