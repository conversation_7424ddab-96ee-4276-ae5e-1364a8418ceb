list=[{
"base": &"RefCounted",
"class": &"AStar",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://AStar.gd"
}, {
"base": &"Node2D",
"class": &"Character",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://Character.gd"
}, {
"base": &"Node2D",
"class": &"Grid",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://Grid.gd"
}, {
"base": &"RefCounted",
"class": &"MCTS",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://MCTS.gd"
}, {
"base": &"Node2D",
"class": &"PathVisualizer",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://PathVisualizer.gd"
}, {
"base": &"Control",
"class": &"UI",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://UI.gd"
}]
