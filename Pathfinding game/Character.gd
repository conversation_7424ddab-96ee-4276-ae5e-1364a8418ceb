extends Node2D
class_name Character

signal movement_finished

var grid: Grid
var current_grid_pos: Vector2i
var target_path: Array[Vector2i] = []
var current_path_index: int = 0
var is_moving: bool = false
var move_speed: float = 200.0  # pixels per second

var tween: Tween

func _ready():
	# Create a tween for smooth movement
	tween = create_tween()
	tween.finished.connect(_on_movement_complete)

func initialize(grid_ref: Grid, start_pos: Vector2i):
	grid = grid_ref
	current_grid_pos = start_pos
	position = grid.grid_to_world(start_pos)

func _draw():
	# Draw the character as a simple colored box
	var size = Vector2(grid.CELL_SIZE * 0.8, grid.CELL_SIZE * 0.8)
	var rect = Rect2(-size/2, size)
	draw_rect(rect, Color.BLUE, true)
	draw_rect(rect, Color.DARK_BLUE, false, 2)

func move_along_path(path: Array[Vector2i]):
	if path.is_empty() or is_moving:
		return
	
	target_path = path
	current_path_index = 0
	is_moving = true
	
	# Start moving to the first position (should be current position)
	if target_path.size() > 1:
		current_path_index = 1  # Skip the first position as it's current position
		move_to_next_position()

func move_to_next_position():
	if current_path_index >= target_path.size():
		# Reached the end of the path
		is_moving = false
		movement_finished.emit()
		return
	
	var target_grid_pos = target_path[current_path_index]
	var target_world_pos = grid.grid_to_world(target_grid_pos)
	
	# Calculate movement duration based on distance and speed
	var distance = position.distance_to(target_world_pos)
	var duration = distance / move_speed
	
	# Kill any existing tween and create a new one
	if tween:
		tween.kill()
	tween = create_tween()
	tween.finished.connect(_on_movement_complete)
	
	# Animate movement to target position
	tween.tween_property(self, "position", target_world_pos, duration)
	tween.tween_callback(func(): current_grid_pos = target_grid_pos)

func _on_movement_complete():
	current_path_index += 1
	
	if current_path_index < target_path.size():
		# Continue to next position
		move_to_next_position()
	else:
		# Finished the entire path
		is_moving = false
		movement_finished.emit()

func get_current_position() -> Vector2i:
	return current_grid_pos

func is_currently_moving() -> bool:
	return is_moving
