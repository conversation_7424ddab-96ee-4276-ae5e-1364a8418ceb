[gd_scene load_steps=16 format=3 uid="uid://bmjnhcql3jny7"]

[ext_resource type="Script" uid="uid://c467vj36seabs" path="res://framed_image.gd" id="1_nrbwx"]
[ext_resource type="Texture2D" uid="uid://cunruirofp5f6" path="res://2024-10-12 19.41.50 nastava.foi.hr 7636f4b79d35.png" id="2_4u50t"]

[sub_resource type="PhysicsMaterial" id="PhysicsMaterial_lagcx"]
rough = true

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_qw3gy"]
albedo_color = Color(1, 0.550091, 0.985493, 1)
albedo_texture = ExtResource("2_4u50t")
emission_enabled = true
emission = Color(1, 1, 1, 1)
emission_energy_multiplier = 0.1
backlight = Color(1, 1, 1, 1)
billboard_keep_scale = true

[sub_resource type="PlaneMesh" id="PlaneMesh_x5h4l"]
material = SubResource("StandardMaterial3D_qw3gy")
size = Vector2(1.6, 0.9)
orientation = 2

[sub_resource type="BoxShape3D" id="BoxShape3D_00e3t"]
size = Vector3(1.6, 1.1, 0.2)

[sub_resource type="Gradient" id="Gradient_leowd"]
offsets = PackedFloat32Array(0.484127, 0.746032)

[sub_resource type="FastNoiseLite" id="FastNoiseLite_xvrdp"]

[sub_resource type="NoiseTexture2D" id="NoiseTexture2D_8f4nu"]
seamless = true
color_ramp = SubResource("Gradient_leowd")
noise = SubResource("FastNoiseLite_xvrdp")

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_braqh"]
albedo_color = Color(0.233925, 0.233925, 0.233925, 1)
albedo_texture = SubResource("NoiseTexture2D_8f4nu")
metallic = 1.0
clearcoat_enabled = true

[sub_resource type="PrismMesh" id="PrismMesh_a7uut"]
material = SubResource("StandardMaterial3D_braqh")
size = Vector3(0.2, 0.2, 0.2)

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_8txqf"]

[sub_resource type="BoxMesh" id="BoxMesh_7w3wv"]
material = SubResource("StandardMaterial3D_8txqf")
size = Vector3(0.01, 0.9, 0.01)

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_jbtle"]

[sub_resource type="BoxMesh" id="BoxMesh_j7m07"]
material = SubResource("StandardMaterial3D_jbtle")
size = Vector3(1.61, 0.01, 0.01)

[node name="Framed image" type="Node3D"]
script = ExtResource("1_nrbwx")

[node name="StaticBody3D" type="RigidBody3D" parent="."]
mass = 3.0
physics_material_override = SubResource("PhysicsMaterial_lagcx")

[node name="Image plane" type="MeshInstance3D" parent="StaticBody3D"]
mesh = SubResource("PlaneMesh_x5h4l")

[node name="CollisionShape3D4" type="CollisionShape3D" parent="StaticBody3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, -0.0501066, 0)
shape = SubResource("BoxShape3D_00e3t")

[node name="Stand" type="Node3D" parent="StaticBody3D"]

[node name="MeshInstance3D2" type="MeshInstance3D" parent="StaticBody3D/Stand"]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 0.6, -0.5, 0)
mesh = SubResource("PrismMesh_a7uut")

[node name="MeshInstance3D" type="MeshInstance3D" parent="StaticBody3D/Stand"]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -0.6, -0.5, 0)
mesh = SubResource("PrismMesh_a7uut")

[node name="Frame" type="Node3D" parent="StaticBody3D"]

[node name="MeshInstance3D2" type="MeshInstance3D" parent="StaticBody3D/Frame"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -0.8, 0, 0)
mesh = SubResource("BoxMesh_7w3wv")
skeleton = NodePath("../../..")

[node name="MeshInstance3D3" type="MeshInstance3D" parent="StaticBody3D/Frame"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0.8, 0, 0)
mesh = SubResource("BoxMesh_7w3wv")
skeleton = NodePath("../../..")

[node name="MeshInstance3D4" type="MeshInstance3D" parent="StaticBody3D/Frame"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0.45, 0)
mesh = SubResource("BoxMesh_j7m07")

[node name="MeshInstance3D5" type="MeshInstance3D" parent="StaticBody3D/Frame"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, -0.45, 0)
mesh = SubResource("BoxMesh_j7m07")
