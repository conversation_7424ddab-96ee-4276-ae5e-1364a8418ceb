[gd_scene load_steps=17 format=3 uid="uid://ci1ldqgybxf5l"]

[ext_resource type="PackedScene" uid="uid://bmjnhcql3jny7" path="res://framed_image.tscn" id="2_iph74"]
[ext_resource type="Texture2D" uid="uid://c27cpw10c84t7" path="res://20230219_100024.jpg" id="2_rufxj"]
[ext_resource type="Texture2D" uid="uid://cunruirofp5f6" path="res://2024-10-12 19.41.50 nastava.foi.hr 7636f4b79d35.png" id="3_2g57w"]

[sub_resource type="ProceduralSkyMaterial" id="ProceduralSkyMaterial_0u6gd"]
sky_top_color = Color(0.458824, 1, 1, 1)
sky_horizon_color = Color(0.8, 0.79225, 0.78325, 1)
ground_horizon_color = Color(0.8, 0.79225, 0.78325, 1)

[sub_resource type="Sky" id="Sky_nccrg"]
sky_material = SubResource("ProceduralSkyMaterial_0u6gd")

[sub_resource type="Environment" id="Environment_gbhqh"]
background_mode = 2
sky = SubResource("Sky_nccrg")
ambient_light_source = 2
ambient_light_color = Color(1, 1, 1, 1)
ambient_light_sky_contribution = 0.0
tonemap_mode = 2
glow_enabled = true

[sub_resource type="Gradient" id="Gradient_4aije"]

[sub_resource type="GradientTexture2D" id="GradientTexture2D_khqxp"]
gradient = SubResource("Gradient_4aije")
fill = 1
fill_from = Vector2(0.763158, 0.773684)
fill_to = Vector2(0.163158, 0.136842)

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_ycfqr"]
albedo_texture = SubResource("GradientTexture2D_khqxp")

[sub_resource type="PlaneMesh" id="PlaneMesh_w84cs"]
material = SubResource("StandardMaterial3D_ycfqr")
size = Vector2(5, 5)

[sub_resource type="ConvexPolygonShape3D" id="ConvexPolygonShape3D_2cenj"]
points = PackedVector3Array(-2.5, 0, -2.5, -2.5, 0, 2.5, 2.5, 0, -2.5, 2.5, 0, 2.5)

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_h42b8"]

[sub_resource type="BoxMesh" id="BoxMesh_7kbii"]
material = SubResource("StandardMaterial3D_h42b8")
size = Vector3(2, 0.1, 1)

[sub_resource type="ConvexPolygonShape3D" id="ConvexPolygonShape3D_y118y"]
points = PackedVector3Array(-1, -0.05, -0.5, -1, 0.05, -0.5, 1, -0.05, -0.5, -1, -0.05, 0.5, -1, 0.05, 0.5, 1, 0.05, -0.5, 1, -0.05, 0.5, 1, 0.05, 0.5)

[sub_resource type="SphereMesh" id="SphereMesh_mmn3g"]

[sub_resource type="ConvexPolygonShape3D" id="ConvexPolygonShape3D_lbe5l"]
points = PackedVector3Array(-0.499434, -0.0238842, 0, -0.497087, -0.0238842, 0.0488874, -0.499434, 0.0237861, 0, -0.497087, -0.0238842, -0.0489852, -0.494936, -0.0711629, 0, -0.489852, -0.0238842, 0.0973837, -0.497087, 0.0237861, 0.0488874, -0.492589, -0.0711629, 0.0484963, -0.485452, -0.0711629, 0.0965037, -0.497087, 0.0237861, -0.0489852, -0.494936, 0.0710649, 0, -0.489852, -0.0238842, -0.0974815, -0.485452, -0.0711629, -0.0966015, -0.492589, -0.0711629, -0.0485941, -0.485941, -0.117952, 0, -0.478021, -0.0238842, 0.144902, -0.489852, 0.0237861, 0.0973837, -0.485452, 0.0710649, 0.0965037, -0.492589, 0.0710649, 0.0484963, -0.483594, -0.117952, 0.0476163, -0.476652, -0.117952, 0.0947438, -0.473621, -0.0711629, 0.143631, -0.492589, 0.0710649, -0.0485941, -0.485452, 0.0710649, -0.0966015, -0.489852, 0.0237861, -0.0974815, -0.485941, 0.117854, 0, -0.478021, -0.0238842, -0.145, -0.473621, -0.0711629, -0.143729, -0.476652, -0.117952, -0.0948415, -0.483594, -0.117952, -0.0477141, -0.470297, -0.163567, -0.0463453, -0.472546, -0.163567, 0, -0.470297, -0.163567, 0.0462475, -0.461497, -0.0238842, 0.191052, -0.478021, 0.0237861, 0.144902, -0.457293, -0.0711629, 0.18939, -0.473621, 0.0710649, 0.143631, -0.476652, 0.117854, 0.0947438, -0.483594, 0.117854, 0.0476163, -0.465017, -0.117952, 0.140991, -0.463453, -0.163567, 0.0921039, -0.452208, -0.163567, 0.13708, -0.483594, 0.117854, -0.0477141, -0.476652, 0.117854, -0.0948415, -0.478021, 0.0237861, -0.145, -0.473621, 0.0710649, -0.143729, -0.470297, 0.163469, 0.0462475, -0.472546, 0.163469, 0, -0.470297, 0.163469, -0.0463453, -0.461497, -0.0238842, -0.19115, -0.457293, -0.0711629, -0.189488, -0.465017, -0.117952, -0.141089, -0.452208, -0.163567, -0.137178, -0.463453, -0.163567, -0.0922016, -0.446146, -0.207713, -0.0887795, -0.452697, -0.207713, -0.0445853, -0.454848, -0.207713, 0, -0.452697, -0.207713, 0.0444876, -0.446146, -0.207713, 0.0886817, -0.440475, -0.0238842, 0.235344, -0.461497, 0.0237861, 0.191052, -0.436564, -0.0711629, 0.233291, -0.457293, 0.0710649, 0.18939, -0.448982, -0.117952, 0.18587, -0.465017, 0.117854, 0.140991, -0.452208, 0.163469, 0.13708, -0.463453, 0.163469, 0.0921039, -0.436564, -0.163567, 0.180786, -0.435293, -0.207713, 0.131996, -0.463453, 0.163469, -0.0922016, -0.452208, 0.163469, -0.137178, -0.465017, 0.117854, -0.141089, -0.457293, 0.0710649, -0.189488, -0.461497, 0.0237861, -0.19115, -0.446146, 0.207615, 0.0886817, -0.452697, 0.207615, 0.0444876, -0.454848, 0.207615, 0, -0.452697, 0.207615, -0.0445853, -0.446146, 0.207615, -0.0887795, -0.440475, -0.0238842, -0.235442, -0.436564, -0.0711629, -0.233388, -0.448982, -0.117952, -0.185968, -0.436564, -0.163567, -0.180883, -0.435293, -0.207713, -0.132094, -0.424734, -0.25, -0.0844774, -0.430991, -0.25, -0.042532, -0.433045, -0.25, 0, -0.430991, -0.25, 0.0424342, -0.424734, -0.25, 0.0843796, -0.415347, -0.0238842, 0.277387, -0.440475, 0.0237861, 0.235344, -0.436564, 0.0710649, 0.233291, -0.411534, -0.0711629, 0.274943, -0.428547, -0.117952, 0.228989, -0.404103, -0.117952, 0.269858, -0.448982, 0.117854, 0.18587, -0.416716, -0.163567, 0.222731, -0.436564, 0.163469, 0.180786, -0.435293, 0.207615, 0.131996, -0.420236, -0.207713, 0.174039, -0.41437, -0.25, 0.125641, -0.400094, -0.25, 0.16563, -0.435293, 0.207615, -0.132094, -0.436564, 0.163469, -0.180883, -0.448982, 0.117854, -0.185968, -0.436564, 0.0710649, -0.233388, -0.440475, 0.0237861, -0.235442, -0.424734, 0.25, 0.0843796, -0.430991, 0.25, 0.0424342, -0.433045, 0.25, 0, -0.430991, 0.25, -0.042532, -0.424734, 0.25, -0.0844774, -0.415347, -0.0238842, -0.277485, -0.411534, -0.0711629, -0.275041, -0.404103, -0.117952, -0.269956, -0.428547, -0.117952, -0.229086, -0.416716, -0.163567, -0.222829, -0.420236, -0.207713, -0.174137, -0.400094, -0.25, -0.165728, -0.41437, -0.25, -0.125738, -0.389828, -0.290035, -0.118308, -0.399508, -0.290035, -0.0794909, -0.405374, -0.290035, -0.0399899, -0.40733, -0.290035, 0, -0.405374, -0.290035, 0.0398921, -0.399508, -0.290035, 0.0793931, -0.389828, -0.290035, 0.11821, -0.386113, -0.0238842, 0.31679, -0.415347, 0.0237861, 0.277387, -0.382593, -0.0711629, 0.313955, -0.411534, 0.0710649, 0.274943, -0.404103, 0.117854, 0.269858, -0.428547, 0.117854, 0.228989, -0.375651, -0.117952, 0.308186, -0.392957, -0.163567, 0.262428, -0.416716, 0.163469, 0.222731, -0.40117, -0.207713, 0.214322, -0.420236, 0.207615, 0.174039, -0.400094, 0.25, 0.16563, -0.41437, 0.25, 0.125641, -0.381908, -0.25, 0.204056, -0.376335, -0.290035, 0.155853, -0.41437, 0.25, -0.125738, -0.400094, 0.25, -0.165728, -0.420236, 0.207615, -0.174137, -0.416716, 0.163469, -0.222829, -0.428547, 0.117854, -0.229086, -0.404103, 0.117854, -0.269956, -0.411534, 0.0710649, -0.275041, -0.415347, 0.0237861, -0.277485, -0.389828, 0.289937, 0.11821, -0.399508, 0.289937, 0.0793931, -0.405374, 0.289937, 0.0398921, -0.40733, 0.289937, 0, -0.405374, 0.289937, -0.0399899, -0.399508, 0.289937, -0.0794909, -0.389828, 0.289937, -0.118308, -0.386113, -0.0238842, -0.316888, -0.382593, -0.0711629, -0.314053, -0.375651, -0.117952, -0.308284, -0.392957, -0.163567, -0.262525, -0.40117, -0.207713, -0.21442, -0.381908, -0.25, -0.204154, -0.376335, -0.290035, -0.155951, -0.361669, -0.327525, -0.109703, -0.370664, -0.327525, -0.0737222, -0.37614, -0.327525, -0.0370567, -0.3779, -0.327525, 0, -0.37614, -0.327525, 0.0369589, -0.370664, -0.327525, 0.0736244, -0.361669, -0.327525, 0.109606, -0.353163, -0.0238842, 0.353065, -0.386113, 0.0237861, 0.31679, -0.350034, -0.0711629, 0.349936, -0.382593, 0.0710649, 0.313955, -0.343678, -0.117952, 0.343581, -0.375651, 0.117854, 0.308186, -0.392957, 0.163469, 0.262428, -0.365287, -0.163567, 0.29968, -0.378193, -0.207713, 0.25265, -0.40117, 0.207615, 0.214322, -0.360105, -0.25, 0.240526, -0.381908, 0.25, 0.204056, -0.376335, 0.289937, 0.155853, -0.359225, -0.290035, 0.191932, -0.349154, -0.327525, 0.144511, -0.333314, -0.327525, 0.178048, -0.376335, 0.289937, -0.155951, -0.381908, 0.25, -0.204154, -0.40117, 0.207615, -0.21442, -0.392957, 0.163469, -0.262525, -0.375651, 0.117854, -0.308284, -0.382593, 0.0710649, -0.314053, -0.386113, 0.0237861, -0.316888, -0.361669, 0.327427, 0.109606, -0.370664, 0.327427, 0.0736244, -0.37614, 0.327427, 0.0369589, -0.3779, 0.327427, 0, -0.37614, 0.327427, -0.0370567, -0.370664, 0.327427, -0.0737222, -0.361669, 0.327427, -0.109703, -0.353163, -0.0238842, -0.353163, -0.350034, -0.0711629, -0.350034, -0.343678, -0.117952, -0.343678, -0.365287, -0.163567, -0.299778, -0.378193, -0.207713, -0.252748, -0.360105, -0.25, -0.240624, -0.359225, -0.290035, -0.19203, -0.333314, -0.327525, -0.178146, -0.349154, -0.327525, -0.144609, -0.318844, -0.361883, -0.132094, -0.330185, -0.361883, -0.100219, -0.338496, -0.361883, -0.0673668, -0.343385, -0.361883, -0.0338301, -0.345047, -0.361883, 0, -0.343385, -0.361883, 0.0337323, -0.338496, -0.361883, 0.0672691, -0.330185, -0.361883, 0.100121, -0.318844, -0.361883, 0.131996, -0.316888, -0.0238842, 0.386015, -0.353163, 0.0237861, 0.353065, -0.350034, 0.0710649, 0.349936, -0.314053, -0.0711629, 0.382495, -0.343678, 0.117854, 0.343581, -0.308284, -0.117952, 0.375553, -0.334194, -0.163567, 0.334097, -0.299778, -0.163567, 0.365189, -0.365287, 0.163469, 0.29968, -0.378193, 0.207615, 0.25265, -0.351598, -0.207713, 0.288436, -0.321679, -0.207713, 0.321581, -0.334781, -0.25, 0.274649, -0.360105, 0.25, 0.240526, -0.338692, -0.290035, 0.226251, -0.314933, -0.290035, 0.258321, -0.359225, 0.289937, 0.191932, -0.333314, 0.327427, 0.178048, -0.349154, 0.327427, 0.144511, -0.314248, -0.327525, 0.209923, -0.304373, -0.361883, 0.162599, -0.349154, 0.327427, -0.144609, -0.333314, 0.327427, -0.178146, -0.359225, 0.289937, -0.19203, -0.360105, 0.25, -0.240624, -0.378193, 0.207615, -0.252748, -0.365287, 0.163469, -0.299778, -0.343678, 0.117854, -0.343678, -0.350034, 0.0710649, -0.350034, -0.353163, 0.0237861, -0.353163, -0.318844, 0.361785, 0.131996, -0.330185, 0.361785, 0.100121, -0.338496, 0.361785, 0.0672691, -0.343385, 0.361785, 0.0337323, -0.345047, 0.361785, 0, -0.343385, 0.361785, -0.0338301, -0.338496, 0.361785, -0.0673668, -0.330185, 0.361785, -0.100219, -0.318844, 0.361785, -0.132094, -0.316888, -0.0238842, -0.386113, -0.314053, -0.0711629, -0.382593, -0.308284, -0.117952, -0.375651, -0.299778, -0.163567, -0.365287, -0.334194, -0.163567, -0.334194, -0.321679, -0.207713, -0.321679, -0.351598, -0.207713, -0.288533, -0.334781, -0.25, -0.274747, -0.314933, -0.290035, -0.258419, -0.338692, -0.290035, -0.226349, -0.314248, -0.327525, -0.21002, -0.304373, -0.361883, -0.162697, -0.2856, -0.393109, -0.118308, -0.295867, -0.393109, -0.0897573, -0.3032, -0.393109, -0.0603271, -0.3076, -0.393109, -0.0303102, -0.309164, -0.393109, 0, -0.3076, -0.393109, 0.0302124, -0.3032, -0.393109, 0.0602293, -0.295867, -0.393109, 0.0896595, -0.2856, -0.393109, 0.11821, -0.277485, -0.0238842, 0.41525, -0.316888, 0.0237861, 0.386015, -0.314053, 0.0710649, 0.382495, -0.275041, -0.0711629, 0.411436, -0.269956, -0.117952, 0.404005, -0.308284, 0.117854, 0.375553, -0.299778, 0.163469, 0.365189, -0.334194, 0.163469, 0.334097, -0.262525, -0.163567, 0.392859, -0.288533, -0.207713, 0.3515, -0.252748, -0.207713, 0.378095, -0.321679, 0.207615, 0.321581, -0.351598, 0.207615, 0.288436, -0.334781, 0.25, 0.274649, -0.306231, -0.25, 0.306133, -0.274747, -0.25, 0.334683, -0.314933, 0.289937, 0.258321, -0.338692, 0.289937, 0.226251, -0.288045, -0.290035, 0.287947, -0.292151, -0.327525, 0.239646, -0.267219, -0.327525, 0.267121, -0.314248, 0.327427, 0.209923, -0.304373, 0.361785, 0.162599, -0.286969, -0.361883, 0.191639, -0.272596, -0.393109, 0.145684, -0.25705, -0.393109, 0.171693, -0.304373, 0.361785, -0.162697, -0.314248, 0.327427, -0.21002, -0.338692, 0.289937, -0.226349, -0.314933, 0.289937, -0.258419, -0.334781, 0.25, -0.274747, -0.351598, 0.207615, -0.288533, -0.321679, 0.207615, -0.321679, -0.334194, 0.163469, -0.334194, -0.308284, 0.117854, -0.375651, -0.314053, 0.0710649, -0.382593, -0.299778, 0.163469, -0.365287, -0.316888, 0.0237861, -0.386113, -0.2856, 0.393011, 0.11821, -0.295867, 0.393011, 0.0896595, -0.3032, 0.393011, 0.0602293, -0.3076, 0.393011, 0.0302124, -0.309164, 0.393011, 0, -0.3076, 0.393011, -0.0303102, -0.3032, 0.393011, -0.0603271, -0.295867, 0.393011, -0.0897573, -0.2856, 0.393011, -0.118308, -0.277485, -0.0238842, -0.415347, -0.275041, -0.0711629, -0.411534, -0.269956, -0.117952, -0.404103, -0.262525, -0.163567, -0.392957, -0.252748, -0.207713, -0.378193, -0.288533, -0.207713, -0.351598, -0.274747, -0.25, -0.334781, -0.306231, -0.25, -0.306231, -0.288045, -0.290035, -0.288045, -0.267219, -0.327525, -0.267219, -0.292151, -0.327525, -0.239744, -0.286969, -0.361883, -0.191736, -0.25705, -0.393109, -0.17179, -0.272596, -0.393109, -0.145782, -0.249815, -0.420713, -0.103544, -0.258712, -0.420713, -0.0785131, -0.265165, -0.420713, -0.0527984, -0.269076, -0.420713, -0.026497, -0.270347, -0.420713, 0, -0.269076, -0.420713, 0.0263992, -0.265165, -0.420713, 0.0527006, -0.258712, -0.420713, 0.0784154, -0.249815, -0.420713, 0.103446, -0.235442, -0.0238842, 0.440378, -0.277485, 0.0237861, 0.41525, -0.233388, -0.0711629, 0.436467, -0.275041, 0.0710649, 0.411436, -0.269956, 0.117854, 0.404005, -0.229086, -0.117952, 0.428449, -0.222829, -0.163567, 0.416618, -0.262525, 0.163469, 0.392859, -0.252748, 0.207615, 0.378095, -0.288533, 0.207615, 0.3515, -0.21442, -0.207713, 0.401072, -0.240624, -0.25, 0.360007, -0.274747, 0.25, 0.334683, -0.306231, 0.25, 0.306133, -0.258419, -0.290035, 0.314835, -0.288045, 0.289937, 0.287947, -0.267219, 0.327427, 0.267121, -0.292151, 0.327427, 0.239646, -0.26673, -0.361883, 0.21882, -0.244046, -0.361883, 0.243948, -0.239744, -0.327525, 0.292053, -0.286969, 0.361785, 0.191639, -0.25705, 0.393011, 0.171693, -0.272596, 0.393011, 0.145684, -0.238962, -0.393109, 0.196038, -0.238473, -0.420713, 0.127401, -0.224784, -0.420713, 0.150084, -0.209043, -0.420713, 0.171399, -0.272596, 0.393011, -0.145782, -0.25705, 0.393011, -0.17179, -0.286969, 0.361785, -0.191736, -0.292151, 0.327427, -0.239744, -0.267219, 0.327427, -0.267219, -0.288045, 0.289937, -0.288045, -0.306231, 0.25, -0.306231, -0.274747, 0.25, -0.334781, -0.288533, 0.207615, -0.351598, -0.269956, 0.117854, -0.404103, -0.262525, 0.163469, -0.392957, -0.275041, 0.0710649, -0.411534, -0.277485, 0.0237861, -0.415347, -0.252748, 0.207615, -0.378193, -0.249815, 0.420615, 0.103446, -0.258712, 0.420615, 0.0784154, -0.265165, 0.420615, 0.0527006, -0.269076, 0.420615, 0.0263992, -0.270347, 0.420615, 0, -0.269076, 0.420615, -0.026497, -0.265165, 0.420615, -0.0527984, -0.258712, 0.420615, -0.0785131, -0.249815, 0.420615, -0.103544, -0.235442, -0.0238842, -0.440475, -0.233388, -0.0711629, -0.436564, -0.229086, -0.117952, -0.428547, -0.222829, -0.163567, -0.416716, -0.21442, -0.207713, -0.40117, -0.240624, -0.25, -0.360105, -0.258419, -0.290035, -0.314933, -0.239744, -0.327525, -0.292151, -0.244046, -0.361883, -0.244046, -0.26673, -0.361883, -0.218918, -0.238962, -0.393109, -0.196136, -0.209043, -0.420713, -0.171497, -0.224784, -0.420713, -0.150182, -0.238473, -0.420713, -0.127498, -0.202101, -0.444499, -0.108041, -0.211682, -0.444499, -0.087704, -0.219309, -0.444499, -0.0665846, -0.224784, -0.444499, -0.0447809, -0.228011, -0.444499, -0.0224882, -0.229184, -0.444499, 0, -0.228011, -0.444499, 0.0223905, -0.224784, -0.444499, 0.0446831, -0.219309, -0.444499, 0.0664869, -0.211682, -0.444499, 0.0876062, -0.202101, -0.444499, 0.107943, -0.19115, -0.0238842, 0.461399, -0.235442, 0.0237861, 0.440378, -0.233388, 0.0710649, 0.436467, -0.189488, -0.0711629, 0.457195, -0.185968, -0.117952, 0.448884, -0.229086, 0.117854, 0.428449, -0.222829, 0.163469, 0.416618, -0.180883, -0.163567, 0.436467, -0.174137, -0.207713, 0.420138, -0.21442, 0.207615, 0.401072, -0.240624, 0.25, 0.360007, -0.204154, -0.25, 0.381811, -0.226349, -0.290035, 0.338594, -0.19203, -0.290035, 0.359127, -0.258419, 0.289937, 0.314835, -0.21002, -0.327525, 0.31415, -0.239744, 0.327427, 0.292053, -0.244046, 0.361785, 0.243948, -0.26673, 0.361785, 0.21882, -0.218918, -0.361883, 0.266632, -0.218624, -0.393109, 0.218527, -0.196136, -0.393109, 0.238864, -0.191736, -0.361883, 0.286871, -0.238962, 0.393011, 0.196038, -0.209043, 0.420615, 0.171399, -0.224784, 0.420615, 0.150084, -0.238473, 0.420615, 0.127401, -0.190563, -0.444499, 0.127205, -0.19115, -0.420713, 0.191052, -0.177168, -0.444499, 0.145293, -0.238473, 0.420615, -0.127498, -0.224784, 0.420615, -0.150182, -0.209043, 0.420615, -0.171497, -0.238962, 0.393011, -0.196136, -0.26673, 0.361785, -0.218918, -0.244046, 0.361785, -0.244046, -0.239744, 0.327427, -0.292151, -0.258419, 0.289937, -0.314933, -0.240624, 0.25, -0.360105, -0.229086, 0.117854, -0.428547, -0.233388, 0.0710649, -0.436564, -0.222829, 0.163469, -0.416716, -0.21442, 0.207615, -0.40117, -0.235442, 0.0237861, -0.440475, -0.202101, 0.444401, 0.107943, -0.211682, 0.444401, 0.0876062, -0.219309, 0.444401, 0.0664869, -0.224784, 0.444401, 0.0446831, -0.228011, 0.444401, 0.0223905, -0.229184, 0.444401, 0, -0.228011, 0.444401, -0.0224882, -0.224784, 0.444401, -0.0447809, -0.219309, 0.444401, -0.0665846, -0.211682, 0.444401, -0.087704, -0.202101, 0.444401, -0.108041, -0.19115, -0.0238842, -0.461497, -0.189488, -0.0711629, -0.457293, -0.185968, -0.117952, -0.448982, -0.180883, -0.163567, -0.436564, -0.174137, -0.207713, -0.420236, -0.204154, -0.25, -0.381908, -0.19203, -0.290035, -0.359225, -0.226349, -0.290035, -0.338692, -0.21002, -0.327525, -0.314248, -0.191736, -0.361883, -0.286969, -0.218918, -0.361883, -0.26673, -0.196136, -0.393109, -0.238962, -0.218624, -0.393109, -0.218624, -0.19115, -0.420713, -0.19115, -0.177168, -0.444499, -0.145391, -0.190563, -0.444499, -0.127303, -0.163968, -0.464272, -0.0876062, -0.171693, -0.464272, -0.07118, -0.177852, -0.464272, -0.0539717, -0.18235, -0.464272, -0.0362744, -0.18499, -0.464272, -0.0182839, -0.18587, -0.464272, 0, -0.18499, -0.464272, 0.0181861, -0.18235, -0.464272, 0.0361767, -0.177852, -0.464272, 0.0538739, -0.171693, -0.464272, 0.0710823, -0.163968, -0.464272, 0.0875085, -0.145, -0.0238842, 0.477923, -0.19115, 0.0237861, 0.461399, -0.189488, 0.0710649, 0.457195, -0.185968, 0.117854, 0.448884, -0.143729, -0.0711629, 0.473523, -0.141089, -0.117952, 0.464919, -0.137178, -0.163567, 0.452111, -0.180883, 0.163469, 0.436467, -0.174137, 0.207615, 0.420138, -0.132094, -0.207713, 0.435196, -0.165728, -0.25, 0.399997, -0.204154, 0.25, 0.381811, -0.19203, 0.289937, 0.359127, -0.226349, 0.289937, 0.338594, -0.155951, -0.290035, 0.376237, -0.178146, -0.327525, 0.333217, -0.21002, 0.327427, 0.31415, -0.162697, -0.361883, 0.304275, -0.191736, 0.361785, 0.286871, -0.218918, 0.361785, 0.266632, -0.196136, 0.393011, 0.238864, -0.218624, 0.393011, 0.218527, -0.171497, -0.420713, 0.208945, -0.17179, -0.393109, 0.256952, -0.19115, 0.420615, 0.191052, -0.177168, 0.444401, 0.145293, -0.190563, 0.444401, 0.127205, -0.154582, -0.464272, 0.103152, -0.143729, -0.464272, 0.117819, -0.162013, -0.444499, 0.161915, -0.145391, -0.444499, 0.17707, -0.190563, 0.444401, -0.127303, -0.177168, 0.444401, -0.145391, -0.19115, 0.420615, -0.19115, -0.218624, 0.393011, -0.218624, -0.196136, 0.393011, -0.238962, -0.218918, 0.361785, -0.26673, -0.191736, 0.361785, -0.286969, -0.21002, 0.327427, -0.314248, -0.226349, 0.289937, -0.338692, -0.19203, 0.289937, -0.359225, -0.204154, 0.25, -0.381908, -0.185968, 0.117854, -0.448982, -0.189488, 0.0710649, -0.457293, -0.19115, 0.0237861, -0.461497, -0.180883, 0.163469, -0.436564, -0.174137, 0.207615, -0.420236, -0.163968, 0.464174, 0.0875085, -0.171693, 0.464174, 0.0710823, -0.177852, 0.464174, 0.0538739, -0.18235, 0.464174, 0.0361767, -0.18499, 0.464174, 0.0181861, -0.18587, 0.464174, 0, -0.18499, 0.464174, -0.0182839, -0.18235, 0.464174, -0.0362744, -0.177852, 0.464174, -0.0539717, -0.171693, 0.464174, -0.07118, -0.163968, 0.464174, -0.0876062, -0.145, -0.0238842, -0.478021, -0.143729, -0.0711629, -0.473621, -0.141089, -0.117952, -0.465017, -0.137178, -0.163567, -0.452208, -0.132094, -0.207713, -0.435293, -0.165728, -0.25, -0.400094, -0.155951, -0.290035, -0.376335, -0.178146, -0.327525, -0.333314, -0.162697, -0.361883, -0.304373, -0.17179, -0.393109, -0.25705, -0.171497, -0.420713, -0.209043, -0.145391, -0.444499, -0.177168, -0.162013, -0.444499, -0.162013, -0.143729, -0.464272, -0.117916, -0.154582, -0.464272, -0.10325, -0.124272, -0.479836, -0.0664869, -0.130236, -0.479836, -0.0539717, -0.134831, -0.479836, -0.0409676, -0.138254, -0.479836, -0.0275725, -0.140209, -0.479836, -0.013884, -0.140893, -0.479836, 0, -0.140209, -0.479836, 0.0137862, -0.138254, -0.479836, 0.0274747, -0.134831, -0.479836, 0.0408699, -0.130236, -0.479836, 0.0538739, -0.124272, -0.479836, 0.0663891, -0.0974815, -0.0238842, 0.489754, -0.145, 0.0237861, 0.477923, -0.0966015, -0.0711629, 0.485354, -0.143729, 0.0710649, 0.473523, -0.141089, 0.117854, 0.464919, -0.137178, 0.163469, 0.452111, -0.0948415, -0.117952, 0.476554, -0.0922016, -0.163567, 0.463355, -0.0887795, -0.207713, 0.446049, -0.132094, 0.207615, 0.435196, -0.165728, 0.25, 0.399997, -0.125738, -0.25, 0.414272, -0.0844774, -0.25, 0.424636, -0.118308, -0.290035, 0.38973, -0.155951, 0.289937, 0.376237, -0.178146, 0.327427, 0.333217, -0.144609, -0.327525, 0.349056, -0.109703, -0.327525, 0.361571, -0.132094, -0.361883, 0.318746, -0.162697, 0.361785, 0.304275, -0.145782, -0.393109, 0.272498, -0.118308, -0.393109, 0.285502, -0.17179, 0.393011, 0.256952, -0.171497, 0.420615, 0.208945, -0.150182, -0.420713, 0.224686, -0.127303, -0.444499, 0.190465, -0.127498, -0.420713, 0.238375, -0.145391, 0.444401, 0.17707, -0.162013, 0.444401, 0.161915, -0.143729, 0.464174, 0.117819, -0.154582, 0.464174, 0.103152, -0.117134, -0.479836, 0.0782199, -0.108921, -0.479836, 0.0892684, -0.0996325, -0.479836, 0.0995347, -0.131409, -0.464272, 0.131312, -0.117916, -0.464272, 0.143631, -0.154582, 0.464174, -0.10325, -0.143729, 0.464174, -0.117916, -0.162013, 0.444401, -0.162013, -0.145391, 0.444401, -0.177168, -0.171497, 0.420615, -0.209043, -0.17179, 0.393011, -0.25705, -0.162697, 0.361785, -0.304373, -0.178146, 0.327427, -0.333314, -0.155951, 0.289937, -0.376335, -0.165728, 0.25, -0.400094, -0.141089, 0.117854, -0.465017, -0.137178, 0.163469, -0.452208, -0.143729, 0.0710649, -0.473621, -0.145, 0.0237861, -0.478021, -0.132094, 0.207615, -0.435293, -0.124272, 0.479738, 0.0663891, -0.130236, 0.479738, 0.0538739, -0.134831, 0.479738, 0.0408699, -0.138254, 0.479738, 0.0274747, -0.140209, 0.479738, 0.0137862, -0.140893, 0.479738, 0, -0.140209, 0.479738, -0.013884, -0.138254, 0.479738, -0.0275725, -0.134831, 0.479738, -0.0409676, -0.130236, 0.479738, -0.0539717, -0.124272, 0.479738, -0.0664869, -0.0974815, -0.0238842, -0.489852, -0.0966015, -0.0711629, -0.485452, -0.0948415, -0.117952, -0.476652, -0.0922016, -0.163567, -0.463453, -0.0887795, -0.207713, -0.446146, -0.0844774, -0.25, -0.424734, -0.125738, -0.25, -0.41437, -0.118308, -0.290035, -0.389828, -0.109703, -0.327525, -0.361669, -0.144609, -0.327525, -0.349154, -0.132094, -0.361883, -0.318844, -0.118308, -0.393109, -0.2856, -0.145782, -0.393109, -0.272596, -0.127498, -0.420713, -0.238473, -0.150182, -0.420713, -0.224784, -0.127303, -0.444499, -0.190563, -0.117916, -0.464272, -0.143729, -0.131409, -0.464272, -0.131409, -0.0996325, -0.479836, -0.0996325, -0.108921, -0.479836, -0.0893662, -0.117134, -0.479836, -0.0783176, -0.0834997, -0.490995, -0.0446831, -0.0875084, -0.490995, -0.0362744, -0.0906372, -0.490995, -0.0274747, -0.0928861, -0.490995, -0.0184794, -0.0942549, -0.490995, -0.00928861, -0.094646, -0.490995, 0, -0.0942549, -0.490995, 0.00919083, -0.0928861, -0.490995, 0.0183817, -0.0906372, -0.490995, 0.0273769, -0.0875084, -0.490995, 0.0361767, -0.0834997, -0.490995, 0.0445853, -0.0489852, -0.0238842, 0.496989, -0.0974815, 0.0237861, 0.489754, -0.0966015, 0.0710649, 0.485354, -0.0485941, -0.0711629, 0.492492, -0.0948415, 0.117854, 0.476554, -0.0922016, 0.163469, 0.463355, -0.0887795, 0.207615, 0.446049, -0.0477141, -0.117952, 0.483496, -0.0463453, -0.163567, 0.470199, -0.0445853, -0.207713, 0.4526, -0.042532, -0.25, 0.430894, -0.0844774, 0.25, 0.424636, -0.125738, 0.25, 0.414272, -0.118308, 0.289937, 0.38973, -0.0794909, -0.290035, 0.39941, -0.0737222, -0.327525, 0.370567, -0.109703, 0.327427, 0.361571, -0.144609, 0.327427, 0.349056, -0.132094, 0.361785, 0.318746, -0.100219, -0.361883, 0.330088, -0.0673668, -0.361883, 0.338399, -0.0897573, -0.393109, 0.295769, -0.118308, 0.393011, 0.285502, -0.145782, 0.393011, 0.272498, -0.103544, -0.420713, 0.249717, -0.127498, 0.420615, 0.238375, -0.150182, 0.420615, 0.224686, -0.127303, 0.444401, 0.190465, -0.108041, -0.444499, 0.202003, -0.10325, -0.464272, 0.154484, -0.0876062, -0.464272, 0.163871, -0.117916, 0.464174, 0.143631, -0.131409, 0.464174, 0.131312, -0.0996325, 0.479738, 0.0995347, -0.108921, 0.479738, 0.0892684, -0.117134, 0.479738, 0.0782199, -0.0787087, -0.490995, 0.052505, -0.0732333, -0.490995, 0.059936, -0.0669757, -0.490995, 0.066878, -0.0893662, -0.479836, 0.108823, -0.0783176, -0.479836, 0.117036, -0.117134, 0.479738, -0.0783176, -0.108921, 0.479738, -0.0893662, -0.0996325, 0.479738, -0.0996325, -0.131409, 0.464174, -0.131409, -0.117916, 0.464174, -0.143729, -0.127303, 0.444401, -0.190563, -0.150182, 0.420615, -0.224784, -0.127498, 0.420615, -0.238473, -0.145782, 0.393011, -0.272596, -0.118308, 0.393011, -0.2856, -0.132094, 0.361785, -0.318844, -0.144609, 0.327427, -0.349154, -0.109703, 0.327427, -0.361669, -0.118308, 0.289937, -0.389828, -0.125738, 0.25, -0.41437, -0.0948415, 0.117854, -0.476652, -0.0922016, 0.163469, -0.463453, -0.0887795, 0.207615, -0.446146, -0.0966015, 0.0710649, -0.485452, -0.0974815, 0.0237861, -0.489852, -0.0844774, 0.25, -0.424734, -0.0834997, 0.490896, 0.0445853, -0.0875084, 0.490896, 0.0361767, -0.0906372, 0.490896, 0.0273769, -0.0928861, 0.490896, 0.0183817, -0.0942549, 0.490896, 0.00919083, -0.094646, 0.490896, 0, -0.0942549, 0.490896, -0.00928861, -0.0928861, 0.490896, -0.0184794, -0.0906372, 0.490896, -0.0274747, -0.0875084, 0.490896, -0.0362744, -0.0834997, 0.490896, -0.0446831, -0.0489852, -0.0238842, -0.497087, -0.0485941, -0.0711629, -0.492589, -0.0477141, -0.117952, -0.483594, -0.0463453, -0.163567, -0.470297, -0.0445853, -0.207713, -0.452697, -0.042532, -0.25, -0.430991, -0.0794909, -0.290035, -0.399508, -0.0737222, -0.327525, -0.370664, -0.0673668, -0.361883, -0.338496, -0.100219, -0.361883, -0.330185, -0.0897573, -0.393109, -0.295867, -0.103544, -0.420713, -0.249815, -0.108041, -0.444499, -0.202101, -0.0876062, -0.464272, -0.163968, -0.10325, -0.464272, -0.154582, -0.0783176, -0.479836, -0.117134, -0.0893662, -0.479836, -0.108921, -0.0669757, -0.490995, -0.0669757, -0.0732333, -0.490995, -0.0600337, -0.0787087, -0.490995, -0.0526028, -0.0395988, -0.497749, -0.026497, -0.0419454, -0.497749, -0.0224882, -0.0439987, -0.497749, -0.0182839, -0.045563, -0.497749, -0.013884, -0.0466386, -0.497749, -0.00928861, -0.047323, -0.497749, -0.00469318, -0.0476163, -0.497749, 0, -0.047323, -0.497749, 0.00459543, -0.0466386, -0.497749, 0.00919083, -0.045563, -0.497749, 0.0137862, -0.0439987, -0.497749, 0.0181861, -0.0419454, -0.497749, 0.0223905, -0.0395988, -0.497749, 0.0263992, 0, -0.0238842, 0.499434, -0.0489852, 0.0237861, 0.496989, -0.0485941, 0.0710649, 0.492492, 0, -0.0711629, 0.494838, 0, -0.117952, 0.485843, -0.0477141, 0.117854, 0.483496, -0.0463453, 0.163469, 0.470199, -0.0445853, 0.207615, 0.4526, -0.042532, 0.25, 0.430894, 0, -0.163567, 0.472448, 0, -0.207713, 0.454751, 0, -0.25, 0.432947, -0.0399899, -0.290035, 0.405277, 0, -0.290035, 0.407232, -0.0794909, 0.289937, 0.39941, -0.0737222, 0.327427, 0.370567, -0.0370567, -0.327525, 0.376042, -0.0673668, 0.361785, 0.338399, -0.100219, 0.361785, 0.330088, -0.0897573, 0.393011, 0.295769, -0.0338301, -0.361883, 0.343287, -0.0603271, -0.393109, 0.303102, -0.0303102, -0.393109, 0.307502, -0.0785131, -0.420713, 0.258614, -0.0527984, -0.420713, 0.265067, -0.103544, 0.420615, 0.249717, -0.087704, -0.444499, 0.211585, -0.0665846, -0.444499, 0.219211, -0.108041, 0.444401, 0.202003, -0.0876062, 0.464174, 0.163871, -0.10325, 0.464174, 0.154484, -0.0664869, -0.479836, 0.124174, -0.0539717, -0.479836, 0.130138, -0.07118, -0.464272, 0.171595, -0.0783176, 0.479738, 0.117036, -0.0893662, 0.479738, 0.108823, -0.0669757, 0.490896, 0.066878, -0.0732333, 0.490896, 0.059936, -0.0787087, 0.490896, 0.052505, -0.0367633, -0.497749, 0.0301146, -0.0336345, -0.497749, 0.0335368, -0.0302124, -0.497749, 0.0366655, -0.0600337, -0.490995, 0.0731355, -0.0526028, -0.490995, 0.0786109, -0.0446831, -0.490995, 0.0834019, -0.0787087, 0.490896, -0.0526028, -0.0732333, 0.490896, -0.0600337, -0.0669757, 0.490896, -0.0669757, -0.0893662, 0.479738, -0.108921, -0.0783176, 0.479738, -0.117134, -0.10325, 0.464174, -0.154582, -0.0876062, 0.464174, -0.163968, -0.108041, 0.444401, -0.202101, -0.103544, 0.420615, -0.249815, -0.0897573, 0.393011, -0.295867, -0.100219, 0.361785, -0.330185, -0.0673668, 0.361785, -0.338496, -0.0737222, 0.327427, -0.370664, -0.0794909, 0.289937, -0.399508, -0.0477141, 0.117854, -0.483594, -0.0485941, 0.0710649, -0.492589, -0.0463453, 0.163469, -0.470297, -0.0445853, 0.207615, -0.452697, -0.042532, 0.25, -0.430991, -0.0489852, 0.0237861, -0.497087, -0.0395988, 0.497651, 0.0263992, -0.0419454, 0.497651, 0.0223905, -0.0439987, 0.497651, 0.0181861, -0.045563, 0.497651, 0.0137862, -0.0466386, 0.497651, 0.00919083, -0.047323, 0.497651, 0.00459543, -0.0476163, 0.497651, 0, -0.047323, 0.497651, -0.00469318, -0.0466386, 0.497651, -0.00928861, -0.045563, 0.497651, -0.013884, -0.0439987, 0.497651, -0.0182839, -0.0419454, 0.497651, -0.0224882, -0.0395988, 0.497651, -0.026497, 0, -0.0238842, -0.499434, 0, -0.0711629, -0.494936, 0, -0.117952, -0.485941, 0, -0.163567, -0.472546, 0, -0.207713, -0.454848, 0, -0.25, -0.433045, 0, -0.290035, -0.40733, -0.0399899, -0.290035, -0.405374, -0.0370567, -0.327525, -0.37614, -0.0338301, -0.361883, -0.343385, -0.0303102, -0.393109, -0.3076, -0.0603271, -0.393109, -0.3032, -0.0527984, -0.420713, -0.265165, -0.0785131, -0.420713, -0.258712, -0.0665846, -0.444499, -0.219309, -0.087704, -0.444499, -0.211682, -0.07118, -0.464272, -0.171693, -0.0539717, -0.479836, -0.130236, -0.0664869, -0.479836, -0.124272, -0.0446831, -0.490995, -0.0834997, -0.0526028, -0.490995, -0.0787087, -0.0600337, -0.490995, -0.0732333, -0.0302124, -0.497749, -0.0367633, -0.0336345, -0.497749, -0.0336345, -0.0367633, -0.497749, -0.0302124, 0, -0.5, 0, 0.0488874, -0.0238842, 0.496989, 0, 0.0237861, 0.499434, 0.0484963, -0.0711629, 0.492492, 0, 0.0710649, 0.494838, 0, 0.117854, 0.485843, 0.0476163, -0.117952, 0.483496, 0.0462475, -0.163567, 0.470199, 0, 0.163469, 0.472448, 0, 0.207615, 0.454751, 0, 0.25, 0.432947, 0, 0.289937, 0.407232, -0.0399899, 0.289937, 0.405277, 0.0444876, -0.207713, 0.4526, 0.0424342, -0.25, 0.430894, 0.0398921, -0.290035, 0.405277, 0, -0.327525, 0.377802, 0.0369589, -0.327525, 0.376042, -0.0370567, 0.327427, 0.376042, 0, -0.361883, 0.34495, -0.0338301, 0.361785, 0.343287, -0.0303102, 0.393011, 0.307502, -0.0603271, 0.393011, 0.303102, -0.0527984, 0.420615, 0.265067, -0.0785131, 0.420615, 0.258614, 0, -0.393109, 0.309066, -0.026497, -0.420713, 0.268978, -0.0447809, -0.444499, 0.224686, -0.0665846, 0.444401, 0.219211, -0.087704, 0.444401, 0.211585, -0.0539717, -0.464272, 0.177755, -0.0362744, -0.464272, 0.182252, -0.07118, 0.464174, 0.171595, -0.0539717, 0.479738, 0.130138, -0.0664869, 0.479738, 0.124174, -0.0362744, -0.490995, 0.0874107, -0.0274747, -0.490995, 0.0905395, -0.0409676, -0.479836, 0.134734, -0.0446831, 0.490896, 0.0834019, -0.0526028, 0.490896, 0.0786109, -0.0600337, 0.490896, 0.0731355, -0.0302124, 0.497651, 0.0366655, -0.0336345, 0.497651, 0.0335368, -0.0367633, 0.497651, 0.0301146, -0.026497, -0.497749, 0.039501, -0.0224882, -0.497749, 0.0418476, -0.0182839, -0.497749, 0.0439009, -0.0367633, 0.497651, -0.0302124, -0.0336345, 0.497651, -0.0336345, -0.0302124, 0.497651, -0.0367633, -0.0600337, 0.490896, -0.0732333, -0.0526028, 0.490896, -0.0787087, -0.0446831, 0.490896, -0.0834997, -0.0664869, 0.479738, -0.124272, -0.0539717, 0.479738, -0.130236, -0.07118, 0.464174, -0.171693, -0.087704, 0.444401, -0.211682, -0.0665846, 0.444401, -0.219309, -0.0785131, 0.420615, -0.258712, -0.0527984, 0.420615, -0.265165, -0.0603271, 0.393011, -0.3032, -0.0303102, 0.393011, -0.3076, -0.0338301, 0.361785, -0.343385, -0.0370567, 0.327427, -0.37614, -0.0399899, 0.289937, -0.405374, 0, 0.117854, -0.485941, 0, 0.0710649, -0.494936, 0, 0.163469, -0.472546, 0, 0.207615, -0.454848, 0, 0.25, -0.433045, 0, 0.289937, -0.40733, 0, 0.0237861, -0.499434, 0, 0.5, 0, 0.0488874, -0.0238842, -0.497087, 0.0484963, -0.0711629, -0.492589, 0.0476163, -0.117952, -0.483594, 0.0462475, -0.163567, -0.470297, 0.0444876, -0.207713, -0.452697, 0.0424342, -0.25, -0.430991, 0.0398921, -0.290035, -0.405374, 0.0369589, -0.327525, -0.37614, 0, -0.327525, -0.3779, 0, -0.361883, -0.345047, 0, -0.393109, -0.309164, -0.026497, -0.420713, -0.269076, -0.0447809, -0.444499, -0.224784, -0.0362744, -0.464272, -0.18235, -0.0539717, -0.464272, -0.177852, -0.0409676, -0.479836, -0.134831, -0.0274747, -0.490995, -0.0906372, -0.0362744, -0.490995, -0.0875084, -0.0182839, -0.497749, -0.0439987, -0.0224882, -0.497749, -0.0419454, -0.026497, -0.497749, -0.0395988, -0.013884, -0.497749, -0.045563, -0.00928861, -0.497749, -0.0466386, -0.00469318, -0.497749, -0.047323, 0, -0.497749, -0.0476163, 0.00459543, -0.497749, -0.047323, 0.00919083, -0.497749, -0.0466386, 0.0137862, -0.497749, -0.045563, 0.0181861, -0.497749, -0.0439987, 0.0223905, -0.497749, -0.0419454, 0.0263992, -0.497749, -0.0395988, 0.0301146, -0.497749, -0.0367633, 0.0335368, -0.497749, -0.0336345, 0.0366655, -0.497749, -0.0302124, 0.039501, -0.497749, -0.026497, 0.0418476, -0.497749, -0.0224882, 0.0439009, -0.497749, -0.0182839, 0.0454653, -0.497749, -0.013884, 0.0465408, -0.497749, -0.00928861, 0.0472252, -0.497749, -0.00469318, 0.0475186, -0.497749, 0, 0.0472252, -0.497749, 0.00459543, 0.0465408, -0.497749, 0.00919083, 0.0454653, -0.497749, 0.0137862, 0.0439009, -0.497749, 0.0181861, 0.0418476, -0.497749, 0.0223905, 0.039501, -0.497749, 0.0263992, 0.0366655, -0.497749, 0.0301146, 0.0335368, -0.497749, 0.0335368, 0.0301146, -0.497749, 0.0366655, 0.0263992, -0.497749, 0.039501, 0.0223905, -0.497749, 0.0418476, 0.0181861, -0.497749, 0.0439009, 0.0137862, -0.497749, 0.0454653, 0.00919083, -0.497749, 0.0465408, 0.00459543, -0.497749, 0.0472252, 0, -0.497749, 0.0475186, -0.00469318, -0.497749, 0.0472252, -0.00928861, -0.497749, 0.0465408, -0.013884, -0.497749, 0.0454653, 0.0973837, -0.0238842, 0.489754, 0.0488874, 0.0237861, 0.496989, 0.0965037, -0.0711629, 0.485354, 0.0484963, 0.0710649, 0.492492, 0.0947438, -0.117952, 0.476554, 0.0476163, 0.117854, 0.483496, 0.0462475, 0.163469, 0.470199, 0.0921039, -0.163567, 0.463355, 0.0886817, -0.207713, 0.446049, 0.0444876, 0.207615, 0.4526, 0.0424342, 0.25, 0.430894, 0.0398921, 0.289937, 0.405277, 0.0369589, 0.327427, 0.376042, 0, 0.327427, 0.377802, 0.0843796, -0.25, 0.424636, 0.0793931, -0.290035, 0.39941, 0.0337323, -0.361883, 0.343287, 0.0672691, -0.361883, 0.338399, 0.0736244, -0.327525, 0.370567, 0, 0.361785, 0.34495, 0, 0.393011, 0.309066, -0.026497, 0.420615, 0.268978, -0.0447809, 0.444401, 0.224686, 0.0302124, -0.393109, 0.307502, 0, -0.420713, 0.27025, 0.0263992, -0.420713, 0.268978, -0.0224882, -0.444499, 0.227913, 0, -0.444499, 0.229086, -0.0182839, -0.464272, 0.184892, -0.0362744, 0.464174, 0.182252, -0.0539717, 0.464174, 0.177755, -0.0275725, -0.479836, 0.138156, -0.0409676, 0.479738, 0.134734, -0.0274747, 0.490896, 0.0905395, -0.0362744, 0.490896, 0.0874107, -0.0184794, -0.490995, 0.0927883, -0.0182839, 0.497651, 0.0439009, -0.0224882, 0.497651, 0.0418476, -0.026497, 0.497651, 0.039501, -0.026497, 0.497651, -0.0395988, -0.0224882, 0.497651, -0.0419454, -0.0182839, 0.497651, -0.0439987, -0.0362744, 0.490896, -0.0875084, -0.0274747, 0.490896, -0.0906372, -0.0409676, 0.479738, -0.134831, -0.0539717, 0.464174, -0.177852, -0.0362744, 0.464174, -0.18235, -0.0447809, 0.444401, -0.224784, -0.026497, 0.420615, -0.269076, 0, 0.393011, -0.309164, 0, 0.361785, -0.345047, 0, 0.327427, -0.3779, 0.0476163, 0.117854, -0.483594, 0.0484963, 0.0710649, -0.492589, 0.0462475, 0.163469, -0.470297, 0.0488874, 0.0237861, -0.497087, 0.0444876, 0.207615, -0.452697, 0.0424342, 0.25, -0.430991, 0.0398921, 0.289937, -0.405374, 0.0369589, 0.327427, -0.37614, 0.0475186, 0.497651, 0, 0.0472252, 0.497651, -0.00469318, 0.0465408, 0.497651, -0.00928861, 0.0454653, 0.497651, -0.013884, 0.0439009, 0.497651, -0.0182839, 0.0418476, 0.497651, -0.0224882, 0.039501, 0.497651, -0.026497, 0.0366655, 0.497651, -0.0302124, 0.0335368, 0.497651, -0.0336345, 0.0301146, 0.497651, -0.0367633, 0.0263992, 0.497651, -0.0395988, 0.0223905, 0.497651, -0.0419454, 0.0181861, 0.497651, -0.0439987, 0.0137862, 0.497651, -0.045563, 0.00919083, 0.497651, -0.0466386, 0.00459543, 0.497651, -0.047323, 0, 0.497651, -0.0476163, -0.00469318, 0.497651, -0.047323, -0.00928861, 0.497651, -0.0466386, -0.013884, 0.497651, -0.045563, -0.013884, 0.497651, 0.0454653, -0.00928861, 0.497651, 0.0465408, -0.00469318, 0.497651, 0.0472252, 0, 0.497651, 0.0475186, 0.00459543, 0.497651, 0.0472252, 0.00919083, 0.497651, 0.0465408, 0.0137862, 0.497651, 0.0454653, 0.0181861, 0.497651, 0.0439009, 0.0223905, 0.497651, 0.0418476, 0.0263992, 0.497651, 0.039501, 0.0301146, 0.497651, 0.0366655, 0.0335368, 0.497651, 0.0335368, 0.0366655, 0.497651, 0.0301146, 0.039501, 0.497651, 0.0263992, 0.0418476, 0.497651, 0.0223905, 0.0439009, 0.497651, 0.0181861, 0.0454653, 0.497651, 0.0137862, 0.0465408, 0.497651, 0.00919083, 0.0472252, 0.497651, 0.00459543, 0.0973837, -0.0238842, -0.489852, 0.0965037, -0.0711629, -0.485452, 0.0947438, -0.117952, -0.476652, 0.0921039, -0.163567, -0.463453, 0.0886817, -0.207713, -0.446146, 0.0843796, -0.25, -0.424734, 0.0793931, -0.290035, -0.399508, 0.0672691, -0.361883, -0.338496, 0.0337323, -0.361883, -0.343385, 0.0736244, -0.327525, -0.370664, 0.0302124, -0.393109, -0.3076, 0.0263992, -0.420713, -0.269076, 0, -0.420713, -0.270347, 0, -0.444499, -0.229184, -0.0224882, -0.444499, -0.228011, -0.0182839, -0.464272, -0.18499, -0.0275725, -0.479836, -0.138254, -0.0184794, -0.490995, -0.0928861, -0.00928861, -0.490995, -0.0942549, 0, -0.490995, -0.094646, 0.00919083, -0.490995, -0.0942549, 0.0183817, -0.490995, -0.0928861, 0.0273769, -0.490995, -0.0906372, 0.0361767, -0.490995, -0.0875084, 0.0445853, -0.490995, -0.0834997, 0.052505, -0.490995, -0.0787087, 0.059936, -0.490995, -0.0732333, 0.066878, -0.490995, -0.0669757, 0.0731355, -0.490995, -0.0600337, 0.0786109, -0.490995, -0.0526028, 0.0834019, -0.490995, -0.0446831, 0.0874107, -0.490995, -0.0362744, 0.0905395, -0.490995, -0.0274747, 0.0927883, -0.490995, -0.0184794, 0.0941571, -0.490995, -0.00928861, 0.0945483, -0.490995, 0, 0.0941571, -0.490995, 0.00919083, 0.0927883, -0.490995, 0.0183817, 0.0905395, -0.490995, 0.0273769, 0.0874107, -0.490995, 0.0361767, 0.0834019, -0.490995, 0.0445853, 0.0786109, -0.490995, 0.052505, 0.0731355, -0.490995, 0.059936, 0.066878, -0.490995, 0.066878, 0.059936, -0.490995, 0.0731355, 0.052505, -0.490995, 0.0786109, 0.0445853, -0.490995, 0.0834019, 0.0361767, -0.490995, 0.0874107, 0.0273769, -0.490995, 0.0905395, 0.0183817, -0.490995, 0.0927883, 0.00919083, -0.490995, 0.0941571, 0, -0.490995, 0.0945483, -0.00928861, -0.490995, 0.0941571, 0.144902, -0.0238842, 0.477923, 0.0973837, 0.0237861, 0.489754, 0.0965037, 0.0710649, 0.485354, 0.143631, -0.0711629, 0.473523, 0.0947438, 0.117854, 0.476554, 0.140991, -0.117952, 0.464919, 0.13708, -0.163567, 0.452111, 0.0921039, 0.163469, 0.463355, 0.0886817, 0.207615, 0.446049, 0.131996, -0.207713, 0.435196, 0.0843796, 0.25, 0.424636, 0.0793931, 0.289937, 0.39941, 0.0736244, 0.327427, 0.370567, 0.0672691, 0.361785, 0.338399, 0.0337323, 0.361785, 0.343287, 0.125641, -0.25, 0.414272, 0.11821, -0.290035, 0.38973, 0.0602293, -0.393109, 0.303102, 0.0896595, -0.393109, 0.295769, 0.100121, -0.361883, 0.330088, 0.109606, -0.327525, 0.361571, 0.0302124, 0.393011, 0.307502, 0.0263992, 0.420615, 0.268978, 0, 0.420615, 0.27025, 0, 0.444401, 0.229086, -0.0224882, 0.444401, 0.227913, -0.0182839, 0.464174, 0.184892, 0.0223905, -0.444499, 0.227913, 0.0446831, -0.444499, 0.224686, 0.0527006, -0.420713, 0.265067, 0, -0.464272, 0.185772, 0.0181861, -0.464272, 0.184892, -0.013884, -0.479836, 0.140111, 0, -0.479836, 0.140796, -0.0275725, 0.479738, 0.138156, -0.0184794, 0.490896, 0.0927883, -0.0184794, 0.490896, -0.0928861, -0.0275725, 0.479738, -0.138254, -0.0182839, 0.464174, -0.18499, -0.0224882, 0.444401, -0.228011, 0, 0.444401, -0.229184, 0, 0.420615, -0.270347, 0.0263992, 0.420615, -0.269076, 0.0302124, 0.393011, -0.3076, 0.0337323, 0.361785, -0.343385, 0.0947438, 0.117854, -0.476652, 0.0965037, 0.0710649, -0.485452, 0.0921039, 0.163469, -0.463453, 0.0886817, 0.207615, -0.446146, 0.0973837, 0.0237861, -0.489852, 0.0843796, 0.25, -0.424734, 0.0793931, 0.289937, -0.399508, 0.0672691, 0.361785, -0.338496, 0.0736244, 0.327427, -0.370664, 0.0941571, 0.490896, 0.00919083, 0.0945483, 0.490896, 0, 0.0941571, 0.490896, -0.00928861, 0.0927883, 0.490896, -0.0184794, 0.0905395, 0.490896, -0.0274747, 0.0874107, 0.490896, -0.0362744, 0.0834019, 0.490896, -0.0446831, 0.0786109, 0.490896, -0.0526028, 0.0731355, 0.490896, -0.0600337, 0.066878, 0.490896, -0.0669757, 0.059936, 0.490896, -0.0732333, 0.052505, 0.490896, -0.0787087, 0.0445853, 0.490896, -0.0834997, 0.0361767, 0.490896, -0.0875084, 0.0273769, 0.490896, -0.0906372, 0.0183817, 0.490896, -0.0928861, 0.00919083, 0.490896, -0.0942549, 0, 0.490896, -0.094646, -0.00928861, 0.490896, -0.0942549, -0.00928861, 0.490896, 0.0941571, 0, 0.490896, 0.0945483, 0.00919083, 0.490896, 0.0941571, 0.0183817, 0.490896, 0.0927883, 0.0273769, 0.490896, 0.0905395, 0.0361767, 0.490896, 0.0874107, 0.0445853, 0.490896, 0.0834019, 0.052505, 0.490896, 0.0786109, 0.059936, 0.490896, 0.0731355, 0.066878, 0.490896, 0.066878, 0.0731355, 0.490896, 0.059936, 0.0786109, 0.490896, 0.052505, 0.0834019, 0.490896, 0.0445853, 0.0874107, 0.490896, 0.0361767, 0.0905395, 0.490896, 0.0273769, 0.0927883, 0.490896, 0.0183817, 0.144902, -0.0238842, -0.478021, 0.143631, -0.0711629, -0.473621, 0.140991, -0.117952, -0.465017, 0.13708, -0.163567, -0.452208, 0.131996, -0.207713, -0.435293, 0.125641, -0.25, -0.41437, 0.11821, -0.290035, -0.389828, 0.100121, -0.361883, -0.330185, 0.0896595, -0.393109, -0.295867, 0.0602293, -0.393109, -0.3032, 0.109606, -0.327525, -0.361669, 0.0527006, -0.420713, -0.265165, 0.0446831, -0.444499, -0.224784, 0.0223905, -0.444499, -0.228011, 0.0181861, -0.464272, -0.18499, 0, -0.464272, -0.18587, 0, -0.479836, -0.140893, -0.013884, -0.479836, -0.140209, 0.0137862, -0.479836, -0.140209, 0.0274747, -0.479836, -0.138254, 0.0408699, -0.479836, -0.134831, 0.0538739, -0.479836, -0.130236, 0.0663891, -0.479836, -0.124272, 0.0782199, -0.479836, -0.117134, 0.0892684, -0.479836, -0.108921, 0.0995347, -0.479836, -0.0996325, 0.108823, -0.479836, -0.0893662, 0.117036, -0.479836, -0.0783176, 0.124174, -0.479836, -0.0664869, 0.130138, -0.479836, -0.0539717, 0.134734, -0.479836, -0.0409676, 0.138156, -0.479836, -0.0275725, 0.140111, -0.479836, -0.013884, 0.140796, -0.479836, 0, 0.140111, -0.479836, 0.0137862, 0.138156, -0.479836, 0.0274747, 0.134734, -0.479836, 0.0408699, 0.130138, -0.479836, 0.0538739, 0.124174, -0.479836, 0.0663891, 0.117036, -0.479836, 0.0782199, 0.108823, -0.479836, 0.0892684, 0.0995347, -0.479836, 0.0995347, 0.0892684, -0.479836, 0.108823, 0.0782199, -0.479836, 0.117036, 0.0663891, -0.479836, 0.124174, 0.0538739, -0.479836, 0.130138, 0.0408699, -0.479836, 0.134734, 0.0274747, -0.479836, 0.138156, 0.0137862, -0.479836, 0.140111, 0.191052, -0.0238842, 0.461399, 0.144902, 0.0237861, 0.477923, 0.18939, -0.0711629, 0.457195, 0.143631, 0.0710649, 0.473523, 0.140991, 0.117854, 0.464919, 0.13708, 0.163469, 0.452111, 0.18587, -0.117952, 0.448884, 0.180786, -0.163567, 0.436467, 0.131996, 0.207615, 0.435196, 0.174039, -0.207713, 0.420138, 0.16563, -0.25, 0.399997, 0.125641, 0.25, 0.414272, 0.11821, 0.289937, 0.38973, 0.109606, 0.327427, 0.361571, 0.100121, 0.361785, 0.330088, 0.0896595, 0.393011, 0.295769, 0.0602293, 0.393011, 0.303102, 0.155853, -0.290035, 0.376237, 0.0784154, -0.420713, 0.258614, 0.103446, -0.420713, 0.249717, 0.11821, -0.393109, 0.285502, 0.131996, -0.361883, 0.318746, 0.144511, -0.327525, 0.349056, 0.0527006, 0.420615, 0.265067, 0.0446831, 0.444401, 0.224686, 0.0223905, 0.444401, 0.227913, 0.0181861, 0.464174, 0.184892, 0, 0.464174, 0.185772, 0, 0.479738, 0.140796, -0.013884, 0.479738, 0.140111, 0.0361767, -0.464272, 0.182252, 0.0664869, -0.444499, 0.219211, -0.013884, 0.479738, -0.140209, 0, 0.479738, -0.140893, 0, 0.464174, -0.18587, 0.0181861, 0.464174, -0.18499, 0.0223905, 0.444401, -0.228011, 0.0446831, 0.444401, -0.224784, 0.0527006, 0.420615, -0.265165, 0.0602293, 0.393011, -0.3032, 0.140991, 0.117854, -0.465017, 0.143631, 0.0710649, -0.473621, 0.13708, 0.163469, -0.452208, 0.144902, 0.0237861, -0.478021, 0.131996, 0.207615, -0.435293, 0.125641, 0.25, -0.41437, 0.11821, 0.289937, -0.389828, 0.100121, 0.361785, -0.330185, 0.109606, 0.327427, -0.361669, 0.0896595, 0.393011, -0.295867, 0.138156, 0.479738, 0.0274747, 0.140111, 0.479738, 0.0137862, 0.140796, 0.479738, 0, 0.140111, 0.479738, -0.013884, 0.138156, 0.479738, -0.0275725, 0.134734, 0.479738, -0.0409676, 0.130138, 0.479738, -0.0539717, 0.124174, 0.479738, -0.0664869, 0.117036, 0.479738, -0.0783176, 0.108823, 0.479738, -0.0893662, 0.0995347, 0.479738, -0.0996325, 0.0892684, 0.479738, -0.108921, 0.0782199, 0.479738, -0.117134, 0.0663891, 0.479738, -0.124272, 0.0538739, 0.479738, -0.130236, 0.0408699, 0.479738, -0.134831, 0.0274747, 0.479738, -0.138254, 0.0137862, 0.479738, -0.140209, 0.0137862, 0.479738, 0.140111, 0.0274747, 0.479738, 0.138156, 0.0408699, 0.479738, 0.134734, 0.0538739, 0.479738, 0.130138, 0.0663891, 0.479738, 0.124174, 0.0782199, 0.479738, 0.117036, 0.0892684, 0.479738, 0.108823, 0.0995347, 0.479738, 0.0995347, 0.108823, 0.479738, 0.0892684, 0.117036, 0.479738, 0.0782199, 0.124174, 0.479738, 0.0663891, 0.130138, 0.479738, 0.0538739, 0.134734, 0.479738, 0.0408699, 0.191052, -0.0238842, -0.461497, 0.18939, -0.0711629, -0.457293, 0.18587, -0.117952, -0.448982, 0.180786, -0.163567, -0.436564, 0.174039, -0.207713, -0.420236, 0.16563, -0.25, -0.400094, 0.155853, -0.290035, -0.376335, 0.131996, -0.361883, -0.318844, 0.11821, -0.393109, -0.2856, 0.103446, -0.420713, -0.249815, 0.0784154, -0.420713, -0.258712, 0.144511, -0.327525, -0.349154, 0.0664869, -0.444499, -0.219309, 0.0361767, -0.464272, -0.18235, 0.0538739, -0.464272, -0.177852, 0.0710823, -0.464272, -0.171693, 0.0875085, -0.464272, -0.163968, 0.103152, -0.464272, -0.154582, 0.117819, -0.464272, -0.143729, 0.131312, -0.464272, -0.131409, 0.143631, -0.464272, -0.117916, 0.154484, -0.464272, -0.10325, 0.163871, -0.464272, -0.0876062, 0.171595, -0.464272, -0.07118, 0.177755, -0.464272, -0.0539717, 0.182252, -0.464272, -0.0362744, 0.184892, -0.464272, -0.0182839, 0.185772, -0.464272, 0, 0.184892, -0.464272, 0.0181861, 0.182252, -0.464272, 0.0361767, 0.177755, -0.464272, 0.0538739, 0.171595, -0.464272, 0.0710823, 0.163871, -0.464272, 0.0875085, 0.154484, -0.464272, 0.103152, 0.143631, -0.464272, 0.117819, 0.131312, -0.464272, 0.131312, 0.117819, -0.464272, 0.143631, 0.103152, -0.464272, 0.154484, 0.0875085, -0.464272, 0.163871, 0.0710823, -0.464272, 0.171595, 0.0538739, -0.464272, 0.177755, 0.235344, -0.0238842, 0.440378, 0.191052, 0.0237861, 0.461399, 0.233291, -0.0711629, 0.436467, 0.18939, 0.0710649, 0.457195, 0.18587, 0.117854, 0.448884, 0.180786, 0.163469, 0.436467, 0.228989, -0.117952, 0.428449, 0.222731, -0.163567, 0.416618, 0.174039, 0.207615, 0.420138, 0.16563, 0.25, 0.399997, 0.214322, -0.207713, 0.401072, 0.204056, -0.25, 0.381811, 0.155853, 0.289937, 0.376237, 0.144511, 0.327427, 0.349056, 0.131996, 0.361785, 0.318746, 0.11821, 0.393011, 0.285502, 0.103446, 0.420615, 0.249717, 0.0784154, 0.420615, 0.258614, 0.191932, -0.290035, 0.359127, 0.178048, -0.327525, 0.333217, 0.0876062, -0.444499, 0.211585, 0.107943, -0.444499, 0.202003, 0.127401, -0.420713, 0.238375, 0.145684, -0.393109, 0.272498, 0.162599, -0.361883, 0.304275, 0.0664869, 0.444401, 0.219211, 0.0361767, 0.464174, 0.182252, 0.0361767, 0.464174, -0.18235, 0.0664869, 0.444401, -0.219309, 0.0784154, 0.420615, -0.258712, 0.18587, 0.117854, -0.448982, 0.18939, 0.0710649, -0.457293, 0.180786, 0.163469, -0.436564, 0.191052, 0.0237861, -0.461497, 0.174039, 0.207615, -0.420236, 0.16563, 0.25, -0.400094, 0.155853, 0.289937, -0.376335, 0.131996, 0.361785, -0.318844, 0.144511, 0.327427, -0.349154, 0.11821, 0.393011, -0.2856, 0.103446, 0.420615, -0.249815, 0.177755, 0.464174, 0.0538739, 0.182252, 0.464174, 0.0361767, 0.184892, 0.464174, 0.0181861, 0.185772, 0.464174, 0, 0.184892, 0.464174, -0.0182839, 0.182252, 0.464174, -0.0362744, 0.177755, 0.464174, -0.0539717, 0.171595, 0.464174, -0.07118, 0.163871, 0.464174, -0.0876062, 0.154484, 0.464174, -0.10325, 0.143631, 0.464174, -0.117916, 0.131312, 0.464174, -0.131409, 0.117819, 0.464174, -0.143729, 0.103152, 0.464174, -0.154582, 0.0875085, 0.464174, -0.163968, 0.0710823, 0.464174, -0.171693, 0.0538739, 0.464174, -0.177852, 0.0538739, 0.464174, 0.177755, 0.0710823, 0.464174, 0.171595, 0.0875085, 0.464174, 0.163871, 0.103152, 0.464174, 0.154484, 0.117819, 0.464174, 0.143631, 0.131312, 0.464174, 0.131312, 0.143631, 0.464174, 0.117819, 0.154484, 0.464174, 0.103152, 0.163871, 0.464174, 0.0875085, 0.171595, 0.464174, 0.0710823, 0.235344, -0.0238842, -0.440475, 0.233291, -0.0711629, -0.436564, 0.228989, -0.117952, -0.428547, 0.222731, -0.163567, -0.416716, 0.214322, -0.207713, -0.40117, 0.204056, -0.25, -0.381908, 0.191932, -0.290035, -0.359225, 0.178048, -0.327525, -0.333314, 0.162599, -0.361883, -0.304373, 0.145684, -0.393109, -0.272596, 0.127401, -0.420713, -0.238473, 0.107943, -0.444499, -0.202101, 0.0876062, -0.444499, -0.211682, 0.127205, -0.444499, -0.190563, 0.145293, -0.444499, -0.177168, 0.161915, -0.444499, -0.162013, 0.17707, -0.444499, -0.145391, 0.190465, -0.444499, -0.127303, 0.202003, -0.444499, -0.108041, 0.211585, -0.444499, -0.087704, 0.219211, -0.444499, -0.0665846, 0.224686, -0.444499, -0.0447809, 0.227913, -0.444499, -0.0224882, 0.229086, -0.444499, 0, 0.227913, -0.444499, 0.0223905, 0.224686, -0.444499, 0.0446831, 0.219211, -0.444499, 0.0664869, 0.211585, -0.444499, 0.0876062, 0.202003, -0.444499, 0.107943, 0.190465, -0.444499, 0.127205, 0.17707, -0.444499, 0.145293, 0.161915, -0.444499, 0.161915, 0.145293, -0.444499, 0.17707, 0.127205, -0.444499, 0.190465, 0.277387, -0.0238842, 0.41525, 0.235344, 0.0237861, 0.440378, 0.233291, 0.0710649, 0.436467, 0.274943, -0.0711629, 0.411436, 0.269858, -0.117952, 0.404005, 0.228989, 0.117854, 0.428449, 0.222731, 0.163469, 0.416618, 0.262428, -0.163567, 0.392859, 0.214322, 0.207615, 0.401072, 0.204056, 0.25, 0.381811, 0.25265, -0.207713, 0.378095, 0.240526, -0.25, 0.360007, 0.191932, 0.289937, 0.359127, 0.178048, 0.327427, 0.333217, 0.162599, 0.361785, 0.304275, 0.145684, 0.393011, 0.272498, 0.127401, 0.420615, 0.238375, 0.107943, 0.444401, 0.202003, 0.0876062, 0.444401, 0.211585, 0.209923, -0.327525, 0.31415, 0.226251, -0.290035, 0.338594, 0.150084, -0.420713, 0.224686, 0.171693, -0.393109, 0.256952, 0.191639, -0.361883, 0.286871, 0.0876062, 0.444401, -0.211682, 0.228989, 0.117854, -0.428547, 0.233291, 0.0710649, -0.436564, 0.222731, 0.163469, -0.416716, 0.235344, 0.0237861, -0.440475, 0.214322, 0.207615, -0.40117, 0.204056, 0.25, -0.381908, 0.191932, 0.289937, -0.359225, 0.178048, 0.327427, -0.333314, 0.162599, 0.361785, -0.304373, 0.145684, 0.393011, -0.272596, 0.107943, 0.444401, -0.202101, 0.127401, 0.420615, -0.238473, 0.219211, 0.444401, 0.0664869, 0.224686, 0.444401, 0.0446831, 0.227913, 0.444401, 0.0223905, 0.229086, 0.444401, 0, 0.227913, 0.444401, -0.0224882, 0.224686, 0.444401, -0.0447809, 0.219211, 0.444401, -0.0665846, 0.211585, 0.444401, -0.087704, 0.202003, 0.444401, -0.108041, 0.190465, 0.444401, -0.127303, 0.17707, 0.444401, -0.145391, 0.161915, 0.444401, -0.162013, 0.145293, 0.444401, -0.177168, 0.127205, 0.444401, -0.190563, 0.127205, 0.444401, 0.190465, 0.145293, 0.444401, 0.17707, 0.161915, 0.444401, 0.161915, 0.17707, 0.444401, 0.145293, 0.190465, 0.444401, 0.127205, 0.202003, 0.444401, 0.107943, 0.211585, 0.444401, 0.0876062, 0.277387, -0.0238842, -0.415347, 0.274943, -0.0711629, -0.411534, 0.269858, -0.117952, -0.404103, 0.262428, -0.163567, -0.392957, 0.25265, -0.207713, -0.378193, 0.240526, -0.25, -0.360105, 0.226251, -0.290035, -0.338692, 0.209923, -0.327525, -0.314248, 0.191639, -0.361883, -0.286969, 0.171693, -0.393109, -0.25705, 0.150084, -0.420713, -0.224784, 0.171399, -0.420713, -0.209043, 0.191052, -0.420713, -0.19115, 0.208945, -0.420713, -0.171497, 0.224686, -0.420713, -0.150182, 0.238375, -0.420713, -0.127498, 0.249717, -0.420713, -0.103544, 0.258614, -0.420713, -0.0785131, 0.265067, -0.420713, -0.0527984, 0.268978, -0.420713, -0.026497, 0.27025, -0.420713, 0, 0.268978, -0.420713, 0.0263992, 0.265067, -0.420713, 0.0527006, 0.258614, -0.420713, 0.0784154, 0.249717, -0.420713, 0.103446, 0.238375, -0.420713, 0.127401, 0.224686, -0.420713, 0.150084, 0.208945, -0.420713, 0.171399, 0.191052, -0.420713, 0.191052, 0.171399, -0.420713, 0.208945, 0.31679, -0.0238842, 0.386015, 0.277387, 0.0237861, 0.41525, 0.313955, -0.0711629, 0.382495, 0.274943, 0.0710649, 0.411436, 0.269858, 0.117854, 0.404005, 0.308186, -0.117952, 0.375553, 0.262428, 0.163469, 0.392859, 0.29968, -0.163567, 0.365189, 0.25265, 0.207615, 0.378095, 0.240526, 0.25, 0.360007, 0.288436, -0.207713, 0.3515, 0.274649, -0.25, 0.334683, 0.258321, -0.290035, 0.314835, 0.226251, 0.289937, 0.338594, 0.209923, 0.327427, 0.31415, 0.191639, 0.361785, 0.286871, 0.171693, 0.393011, 0.256952, 0.150084, 0.420615, 0.224686, 0.239646, -0.327525, 0.292053, 0.196038, -0.393109, 0.238864, 0.21882, -0.361883, 0.266632, 0.269858, 0.117854, -0.404103, 0.274943, 0.0710649, -0.411534, 0.277387, 0.0237861, -0.415347, 0.262428, 0.163469, -0.392957, 0.25265, 0.207615, -0.378193, 0.240526, 0.25, -0.360105, 0.226251, 0.289937, -0.338692, 0.209923, 0.327427, -0.314248, 0.191639, 0.361785, -0.286969, 0.171693, 0.393011, -0.25705, 0.150084, 0.420615, -0.224784, 0.249717, 0.420615, 0.103446, 0.258614, 0.420615, 0.0784154, 0.265067, 0.420615, 0.0527006, 0.268978, 0.420615, 0.0263992, 0.27025, 0.420615, 0, 0.268978, 0.420615, -0.026497, 0.265067, 0.420615, -0.0527984, 0.258614, 0.420615, -0.0785131, 0.249717, 0.420615, -0.103544, 0.238375, 0.420615, -0.127498, 0.224686, 0.420615, -0.150182, 0.208945, 0.420615, -0.171497, 0.191052, 0.420615, -0.19115, 0.171399, 0.420615, -0.209043, 0.171399, 0.420615, 0.208945, 0.191052, 0.420615, 0.191052, 0.208945, 0.420615, 0.171399, 0.224686, 0.420615, 0.150084, 0.238375, 0.420615, 0.127401, 0.31679, -0.0238842, -0.386113, 0.313955, -0.0711629, -0.382593, 0.308186, -0.117952, -0.375651, 0.29968, -0.163567, -0.365287, 0.288436, -0.207713, -0.351598, 0.274649, -0.25, -0.334781, 0.258321, -0.290035, -0.314933, 0.239646, -0.327525, -0.292151, 0.21882, -0.361883, -0.26673, 0.196038, -0.393109, -0.238962, 0.218527, -0.393109, -0.218624, 0.238864, -0.393109, -0.196136, 0.256952, -0.393109, -0.17179, 0.272498, -0.393109, -0.145782, 0.285502, -0.393109, -0.118308, 0.295769, -0.393109, -0.0897573, 0.303102, -0.393109, -0.0603271, 0.307502, -0.393109, -0.0303102, 0.309066, -0.393109, 0, 0.307502, -0.393109, 0.0302124, 0.303102, -0.393109, 0.0602293, 0.295769, -0.393109, 0.0896595, 0.285502, -0.393109, 0.11821, 0.272498, -0.393109, 0.145684, 0.256952, -0.393109, 0.171693, 0.238864, -0.393109, 0.196038, 0.218527, -0.393109, 0.218527, 0.353065, -0.0238842, 0.353065, 0.31679, 0.0237861, 0.386015, 0.349936, -0.0711629, 0.349936, 0.313955, 0.0710649, 0.382495, 0.343581, -0.117952, 0.343581, 0.308186, 0.117854, 0.375553, 0.29968, 0.163469, 0.365189, 0.334097, -0.163567, 0.334097, 0.321581, -0.207713, 0.321581, 0.288436, 0.207615, 0.3515, 0.274649, 0.25, 0.334683, 0.258321, 0.289937, 0.314835, 0.306133, -0.25, 0.306133, 0.267121, -0.327525, 0.267121, 0.287947, -0.290035, 0.287947, 0.239646, 0.327427, 0.292053, 0.21882, 0.361785, 0.266632, 0.196038, 0.393011, 0.238864, 0.243948, -0.361883, 0.243948, 0.308186, 0.117854, -0.375651, 0.313955, 0.0710649, -0.382593, 0.31679, 0.0237861, -0.386113, 0.29968, 0.163469, -0.365287, 0.288436, 0.207615, -0.351598, 0.274649, 0.25, -0.334781, 0.258321, 0.289937, -0.314933, 0.239646, 0.327427, -0.292151, 0.21882, 0.361785, -0.26673, 0.196038, 0.393011, -0.238962, 0.272498, 0.393011, 0.145684, 0.285502, 0.393011, 0.11821, 0.295769, 0.393011, 0.0896595, 0.303102, 0.393011, 0.0602293, 0.307502, 0.393011, 0.0302124, 0.309066, 0.393011, 0, 0.307502, 0.393011, -0.0303102, 0.303102, 0.393011, -0.0603271, 0.295769, 0.393011, -0.0897573, 0.285502, 0.393011, -0.118308, 0.272498, 0.393011, -0.145782, 0.256952, 0.393011, -0.17179, 0.238864, 0.393011, -0.196136, 0.218527, 0.393011, -0.218624, 0.218527, 0.393011, 0.218527, 0.238864, 0.393011, 0.196038, 0.256952, 0.393011, 0.171693, 0.353065, -0.0238842, -0.353163, 0.349936, -0.0711629, -0.350034, 0.343581, -0.117952, -0.343678, 0.334097, -0.163567, -0.334194, 0.321581, -0.207713, -0.321679, 0.306133, -0.25, -0.306231, 0.287947, -0.290035, -0.288045, 0.267121, -0.327525, -0.267219, 0.243948, -0.361883, -0.244046, 0.266632, -0.361883, -0.218918, 0.286871, -0.361883, -0.191736, 0.304275, -0.361883, -0.162697, 0.318746, -0.361883, -0.132094, 0.330088, -0.361883, -0.100219, 0.338399, -0.361883, -0.0673668, 0.343287, -0.361883, -0.0338301, 0.34495, -0.361883, 0, 0.343287, -0.361883, 0.0337323, 0.338399, -0.361883, 0.0672691, 0.330088, -0.361883, 0.100121, 0.318746, -0.361883, 0.131996, 0.304275, -0.361883, 0.162599, 0.286871, -0.361883, 0.191639, 0.266632, -0.361883, 0.21882, 0.386015, -0.0238842, 0.31679, 0.353065, 0.0237861, 0.353065, 0.349936, 0.0710649, 0.349936, 0.382495, -0.0711629, 0.313955, 0.343581, 0.117854, 0.343581, 0.375553, -0.117952, 0.308186, 0.365189, -0.163567, 0.29968, 0.334097, 0.163469, 0.334097, 0.321581, 0.207615, 0.321581, 0.3515, -0.207713, 0.288436, 0.334683, -0.25, 0.274649, 0.306133, 0.25, 0.306133, 0.287947, 0.289937, 0.287947, 0.267121, 0.327427, 0.267121, 0.314835, -0.290035, 0.258321, 0.292053, -0.327525, 0.239646, 0.243948, 0.361785, 0.243948, 0.343581, 0.117854, -0.343678, 0.349936, 0.0710649, -0.350034, 0.353065, 0.0237861, -0.353163, 0.334097, 0.163469, -0.334194, 0.321581, 0.207615, -0.321679, 0.306133, 0.25, -0.306231, 0.287947, 0.289937, -0.288045, 0.267121, 0.327427, -0.267219, 0.243948, 0.361785, -0.244046, 0.304275, 0.361785, 0.162599, 0.318746, 0.361785, 0.131996, 0.330088, 0.361785, 0.100121, 0.338399, 0.361785, 0.0672691, 0.343287, 0.361785, 0.0337323, 0.34495, 0.361785, 0, 0.343287, 0.361785, -0.0338301, 0.338399, 0.361785, -0.0673668, 0.330088, 0.361785, -0.100219, 0.318746, 0.361785, -0.132094, 0.304275, 0.361785, -0.162697, 0.286871, 0.361785, -0.191736, 0.266632, 0.361785, -0.218918, 0.266632, 0.361785, 0.21882, 0.286871, 0.361785, 0.191639, 0.386015, -0.0238842, -0.316888, 0.382495, -0.0711629, -0.314053, 0.375553, -0.117952, -0.308284, 0.365189, -0.163567, -0.299778, 0.3515, -0.207713, -0.288533, 0.334683, -0.25, -0.274747, 0.314835, -0.290035, -0.258419, 0.292053, -0.327525, -0.239744, 0.31415, -0.327525, -0.21002, 0.333217, -0.327525, -0.178146, 0.349056, -0.327525, -0.144609, 0.361571, -0.327525, -0.109703, 0.370567, -0.327525, -0.0737222, 0.376042, -0.327525, -0.0370567, 0.377802, -0.327525, 0, 0.376042, -0.327525, 0.0369589, 0.370567, -0.327525, 0.0736244, 0.361571, -0.327525, 0.109606, 0.349056, -0.327525, 0.144511, 0.333217, -0.327525, 0.178048, 0.31415, -0.327525, 0.209923, 0.41525, -0.0238842, 0.277387, 0.386015, 0.0237861, 0.31679, 0.382495, 0.0710649, 0.313955, 0.411436, -0.0711629, 0.274943, 0.404005, -0.117952, 0.269858, 0.375553, 0.117854, 0.308186, 0.365189, 0.163469, 0.29968, 0.392859, -0.163567, 0.262428, 0.378095, -0.207713, 0.25265, 0.3515, 0.207615, 0.288436, 0.334683, 0.25, 0.274649, 0.360007, -0.25, 0.240526, 0.314835, 0.289937, 0.258321, 0.292053, 0.327427, 0.239646, 0.338594, -0.290035, 0.226251, 0.375553, 0.117854, -0.308284, 0.382495, 0.0710649, -0.314053, 0.365189, 0.163469, -0.299778, 0.386015, 0.0237861, -0.316888, 0.3515, 0.207615, -0.288533, 0.334683, 0.25, -0.274747, 0.314835, 0.289937, -0.258419, 0.292053, 0.327427, -0.239744, 0.31415, 0.327427, 0.209923, 0.333217, 0.327427, 0.178048, 0.349056, 0.327427, 0.144511, 0.361571, 0.327427, 0.109606, 0.370567, 0.327427, 0.0736244, 0.376042, 0.327427, 0.0369589, 0.377802, 0.327427, 0, 0.376042, 0.327427, -0.0370567, 0.370567, 0.327427, -0.0737222, 0.361571, 0.327427, -0.109703, 0.349056, 0.327427, -0.144609, 0.333217, 0.327427, -0.178146, 0.31415, 0.327427, -0.21002, 0.41525, -0.0238842, -0.277485, 0.411436, -0.0711629, -0.275041, 0.404005, -0.117952, -0.269956, 0.392859, -0.163567, -0.262525, 0.378095, -0.207713, -0.252748, 0.360007, -0.25, -0.240624, 0.338594, -0.290035, -0.226349, 0.359127, -0.290035, -0.19203, 0.376237, -0.290035, -0.155951, 0.38973, -0.290035, -0.118308, 0.39941, -0.290035, -0.0794909, 0.405277, -0.290035, -0.0399899, 0.407232, -0.290035, 0, 0.405277, -0.290035, 0.0398921, 0.39941, -0.290035, 0.0793931, 0.38973, -0.290035, 0.11821, 0.376237, -0.290035, 0.155853, 0.359127, -0.290035, 0.191932, 0.440378, -0.0238842, 0.235344, 0.41525, 0.0237861, 0.277387, 0.436467, -0.0711629, 0.233291, 0.411436, 0.0710649, 0.274943, 0.404005, 0.117854, 0.269858, 0.428449, -0.117952, 0.228989, 0.416618, -0.163567, 0.222731, 0.392859, 0.163469, 0.262428, 0.378095, 0.207615, 0.25265, 0.401072, -0.207713, 0.214322, 0.360007, 0.25, 0.240526, 0.381811, -0.25, 0.204056, 0.338594, 0.289937, 0.226251, 0.404005, 0.117854, -0.269956, 0.392859, 0.163469, -0.262525, 0.411436, 0.0710649, -0.275041, 0.41525, 0.0237861, -0.277485, 0.378095, 0.207615, -0.252748, 0.360007, 0.25, -0.240624, 0.338594, 0.289937, -0.226349, 0.359127, 0.289937, 0.191932, 0.376237, 0.289937, 0.155853, 0.38973, 0.289937, 0.11821, 0.39941, 0.289937, 0.0793931, 0.405277, 0.289937, 0.0398921, 0.407232, 0.289937, 0, 0.405277, 0.289937, -0.0399899, 0.39941, 0.289937, -0.0794909, 0.38973, 0.289937, -0.118308, 0.376237, 0.289937, -0.155951, 0.359127, 0.289937, -0.19203, 0.440378, -0.0238842, -0.235442, 0.436467, -0.0711629, -0.233388, 0.428449, -0.117952, -0.229086, 0.416618, -0.163567, -0.222829, 0.401072, -0.207713, -0.21442, 0.381811, -0.25, -0.204154, 0.399997, -0.25, -0.165728, 0.414272, -0.25, -0.125738, 0.424636, -0.25, -0.0844774, 0.430894, -0.25, -0.042532, 0.432947, -0.25, 0, 0.430894, -0.25, 0.0424342, 0.424636, -0.25, 0.0843796, 0.414272, -0.25, 0.125641, 0.399997, -0.25, 0.16563, 0.461399, -0.0238842, 0.191052, 0.440378, 0.0237861, 0.235344, 0.436467, 0.0710649, 0.233291, 0.457195, -0.0711629, 0.18939, 0.448884, -0.117952, 0.18587, 0.428449, 0.117854, 0.228989, 0.416618, 0.163469, 0.222731, 0.420138, -0.207713, 0.174039, 0.436467, -0.163567, 0.180786, 0.401072, 0.207615, 0.214322, 0.381811, 0.25, 0.204056, 0.428449, 0.117854, -0.229086, 0.436467, 0.0710649, -0.233388, 0.416618, 0.163469, -0.222829, 0.401072, 0.207615, -0.21442, 0.440378, 0.0237861, -0.235442, 0.381811, 0.25, -0.204154, 0.399997, 0.25, 0.16563, 0.414272, 0.25, 0.125641, 0.424636, 0.25, 0.0843796, 0.430894, 0.25, 0.0424342, 0.432947, 0.25, 0, 0.430894, 0.25, -0.042532, 0.424636, 0.25, -0.0844774, 0.414272, 0.25, -0.125738, 0.399997, 0.25, -0.165728, 0.461399, -0.0238842, -0.19115, 0.457195, -0.0711629, -0.189488, 0.448884, -0.117952, -0.185968, 0.436467, -0.163567, -0.180883, 0.420138, -0.207713, -0.174137, 0.435196, -0.207713, -0.132094, 0.446049, -0.207713, -0.0887795, 0.4526, -0.207713, -0.0445853, 0.454751, -0.207713, 0, 0.4526, -0.207713, 0.0444876, 0.446049, -0.207713, 0.0886817, 0.435196, -0.207713, 0.131996, 0.477923, -0.0238842, 0.144902, 0.461399, 0.0237861, 0.191052, 0.457195, 0.0710649, 0.18939, 0.448884, 0.117854, 0.18587, 0.473523, -0.0711629, 0.143631, 0.464919, -0.117952, 0.140991, 0.452111, -0.163567, 0.13708, 0.436467, 0.163469, 0.180786, 0.420138, 0.207615, 0.174039, 0.448884, 0.117854, -0.185968, 0.457195, 0.0710649, -0.189488, 0.461399, 0.0237861, -0.19115, 0.436467, 0.163469, -0.180883, 0.420138, 0.207615, -0.174137, 0.435196, 0.207615, 0.131996, 0.446049, 0.207615, 0.0886817, 0.4526, 0.207615, 0.0444876, 0.454751, 0.207615, 0, 0.4526, 0.207615, -0.0445853, 0.446049, 0.207615, -0.0887795, 0.435196, 0.207615, -0.132094, 0.477923, -0.0238842, -0.145, 0.473523, -0.0711629, -0.143729, 0.464919, -0.117952, -0.141089, 0.452111, -0.163567, -0.137178, 0.463355, -0.163567, -0.0922016, 0.470199, -0.163567, -0.0463453, 0.472448, -0.163567, 0, 0.470199, -0.163567, 0.0462475, 0.463355, -0.163567, 0.0921039, 0.485354, -0.0711629, 0.0965037, 0.489754, -0.0238842, 0.0973837, 0.477923, 0.0237861, 0.144902, 0.473523, 0.0710649, 0.143631, 0.464919, 0.117854, 0.140991, 0.452111, 0.163469, 0.13708, 0.476554, -0.117952, 0.0947438, 0.464919, 0.117854, -0.141089, 0.452111, 0.163469, -0.137178, 0.473523, 0.0710649, -0.143729, 0.477923, 0.0237861, -0.145, 0.463355, 0.163469, 0.0921039, 0.470199, 0.163469, 0.0462475, 0.472448, 0.163469, 0, 0.470199, 0.163469, -0.0463453, 0.463355, 0.163469, -0.0922016, 0.489754, -0.0238842, -0.0974815, 0.485354, -0.0711629, -0.0966015, 0.476554, -0.117952, -0.0948415, 0.483496, -0.117952, -0.0477141, 0.485843, -0.117952, 0, 0.483496, -0.117952, 0.0476163, 0.492492, -0.0711629, 0.0484963, 0.496989, -0.0238842, 0.0488874, 0.489754, 0.0237861, 0.0973837, 0.485354, 0.0710649, 0.0965037, 0.476554, 0.117854, 0.0947438, 0.476554, 0.117854, -0.0948415, 0.485354, 0.0710649, -0.0966015, 0.489754, 0.0237861, -0.0974815, 0.483496, 0.117854, 0.0476163, 0.485843, 0.117854, 0, 0.483496, 0.117854, -0.0477141, 0.496989, -0.0238842, -0.0489852, 0.492492, -0.0711629, -0.0485941, 0.494838, -0.0711629, 0, 0.499434, -0.0238842, 0, 0.496989, 0.0237861, 0.0488874, 0.492492, 0.0710649, 0.0484963, 0.492492, 0.0710649, -0.0485941, 0.496989, 0.0237861, -0.0489852, 0.494838, 0.0710649, 0, 0.499434, 0.0237861, 0)

[node name="Node3D" type="Node3D"]

[node name="WorldEnvironment" type="WorldEnvironment" parent="."]
environment = SubResource("Environment_gbhqh")

[node name="DirectionalLight3D" type="DirectionalLight3D" parent="."]
transform = Transform3D(-1, 5.21236e-08, -7.01845e-08, -1.37621e-09, 0.793332, 0.608789, 8.74119e-08, 0.608789, -0.793332, 0, 0, 0)
visible = false

[node name="OmniLight3D" type="OmniLight3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -0.1, 1.2253, -0.5)
visible = false

[node name="Camera3D" type="Camera3D" parent="."]
transform = Transform3D(-0.577245, 0, 0.816571, 0, 1, 0, -0.816571, 0, -0.577245, 1, 0.57855, -2)
size = 2.44

[node name="StaticBody3D" type="StaticBody3D" parent="."]

[node name="MeshInstance3D" type="MeshInstance3D" parent="StaticBody3D"]
mesh = SubResource("PlaneMesh_w84cs")
skeleton = NodePath("../..")

[node name="CollisionShape3D" type="CollisionShape3D" parent="StaticBody3D"]
shape = SubResource("ConvexPolygonShape3D_2cenj")

[node name="Node3D" type="Node3D" parent="."]

[node name="Framed image" parent="Node3D" instance=ExtResource("2_iph74")]
transform = Transform3D(-1.0031, 0, 0.0866664, 0, 1.00684, 0, -0.0866664, 0, -1.0031, 0, 1.16581, 0)
texture = ExtResource("2_rufxj")

[node name="Pedestal" type="RigidBody3D" parent="Node3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0.188755, 0)

[node name="MeshInstance3D2" type="MeshInstance3D" parent="Node3D/Pedestal"]
mesh = SubResource("BoxMesh_7kbii")

[node name="CollisionShape3D" type="CollisionShape3D" parent="Node3D/Pedestal"]
shape = SubResource("ConvexPolygonShape3D_y118y")

[node name="Node3D2" type="Node3D" parent="."]
transform = Transform3D(-0.0523533, 0, -0.998629, 0, 1, 0, 0.998629, 0, -0.0523533, -1.45732, 0.947597, -1.65212)

[node name="Framed image" parent="Node3D2" instance=ExtResource("2_iph74")]
transform = Transform3D(-0.996602, 0, 0.143222, 0, 1.00684, 0, -0.143222, 0, -0.996602, 0, 0.829415, 0)
texture = ExtResource("3_2g57w")

[node name="RigidBody3D" type="RigidBody3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -1.23514, 4.20286, -1.64871)
mass = 0.5

[node name="MeshInstance3D" type="MeshInstance3D" parent="RigidBody3D"]
mesh = SubResource("SphereMesh_mmn3g")

[node name="CollisionShape3D" type="CollisionShape3D" parent="RigidBody3D"]
shape = SubResource("ConvexPolygonShape3D_lbe5l")
