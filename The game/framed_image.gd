extends Node3D

#@export var texture_path: String
@export var texture: CompressedTexture2D

@export_category("Floating effect")
@export var amplitude: float = randf_range(0.001, 0.006)  # How far it floats above/below its initial y
@export var speed: float = 2.0     # How fast it floats
@export var floating: bool = false


@onready var mesh_instance: MeshInstance3D = $"StaticBody3D/Image plane"
@onready var initial_y: float = global_transform.origin.y

func set_image():
    """
    Dynamically change the texture at runtime.
    """
    #if not texture and texture_path:
        #var texture = load(texture_path)

    if texture and mesh_instance:
        var material = StandardMaterial3D.new()
        material.albedo_texture = texture
        mesh_instance.material_override = material


# Called when the node enters the scene tree for the first time.
func _ready() -> void:
    self.set_image()
    $StaticBody3D/Stand.visible = not floating
    pass # Replace with function body.


# Called every frame. 'delta' is the elapsed time since the previous frame.
func _process(delta: float) -> void:
    if floating:
        # Calculate a "bobbing" offset based on sine wave
        var offset = sin(Time.get_ticks_msec() / 1000.0 * speed) * amplitude

        # Apply the offset to the current y position
        var current_transform = global_transform
        current_transform.origin.y = initial_y + offset
        global_transform = current_transform
