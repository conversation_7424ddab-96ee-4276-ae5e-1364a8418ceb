% !TeX root = ../main.tex

\chapter{Umjetna inteligencija kao igrač}

\chapterAbstract{%
    U ovom poglavlju istražujemo kako se \gls{uint} može koristiti za automatizaciju igranja videoigara. Iako je zabava jedna od osnovnih svrha videoigara, postoje i drugi razlozi zašto bismo htjeli koristiti \gls{uint} u igrama, kao što su stvaranje izazovnijih i dinamičnijih igara, omogućavanje individualizacije igara te brži razvoj neigrajućih likova. Razmatramo različite dimenzije i faktore korištenja \gls{uint}-ja u igranju, kao i različite metode za igranje igara, uključujući modeliranje ponašanja, poticano učenje, nadzirano učenje i korištenje velikih predtrenirani modela.
}

\vspace{2em}
\section{Uvod}
\label{sec: uvod}

Jedna od osnovnih zadaća igara, pa tako i videoigara, je pružanje zabave. Naravno da postoje i videoigre drugačijih ciljeva, od obrazovnih do ozbiljnih videoigara koje istražuju određenu temu ili su namijenjene simulaciji i testiranju određenih okruženja, no osnovna tema koja se proteže kroz apsolutnu većinu videoigara je upravo zabava.
\marginnote{zabava}%
U više filozofskom kontekstu, moguće je prepoznati konačne i beskonačne igre, engl. \emph{finite} i \emph{infinite games} \cite{carse1986FiniteInfiniteGames}. Upravo je beskonačna igra, ona koja nije opterećena pobjeđivanjem, usmjerena igranju i njena je svrha igranje. Interesantna je i njegova tvrdnja da je igranje moguće jedino kad ono nije prisilno: \blockquote[{\cite[str. 4]{carse1986FiniteInfiniteGames}}]{No one can play who is forced to play.}

S obzirom na sve navedeno, zašto bismo htjeli automatizirati igranje, ako je to jedna od aktivnosti koja igrača vodi do zabave?

U ovom poglavlju, pokušat ćemo ponuditi odgovor na to pitanje i istražiti kako se \gls{uint} može koristiti za automatizaciju igranja. Iako je zabava jedna od osnovnih svrha videoigara, postoje i drugi razlozi zašto bismo htjeli koristiti \gls{uint} u igrama. Na primjer, \gls{uint} može pomoći u stvaranju izazovnijih i dinamičnijih igara,
\marginnote{izazovnije i dinamičnije igre}%
omogućiti igračima da se fokusiraju na strategiju umjesto na rutinske zadatke, ili čak omogućiti igračima da igraju protiv samih sebe. Nadalje, \gls{uint} može pomoći u stvaranju igara koje su prilagođene individualnim igračima,
\marginnote{individualizacija igara}%
omogućujući im da igraju na način koji im najviše odgovara. Osim toga, \gls{uint} može pomoći u bržem i jednostavnijem
\marginnote{brži i jednostavniji razvoj}%
razvoju neigrajućih likova (engl. \emph{\gls{npc}}) koji reagiraju na ponašanje igrača, čime se smanjuje potreba za ručnim programiranjem i testiranjem.



\section[Zašto koristiti UI u igranju?]{Zašto koristiti \gls{uint} u igranju?}
\label{sec: zašto koristiti UI u igranju}

U osnovnom obliku, moguće je korištenje \gls{uint}-ja u igranju igara promatrati kroz dvije različite dimenzije \cite[str. 138-139]{yannakakis2025ArtificialIntelligenceGames}:
\marginnote{dimenzije \gls{uint}-ja u igranju}%
jednu, koja predstavlja ulogu \gls{uint}-ja
\marginnote{uloga \gls{uint}-ja}%
u igranju videoigara, a na svojim suprotnim stranama ima igrača i neigrajuće likove, tj. ne-igrača te drugu, koja opisuje cilj
\marginnote{cilj \gls{uint}-ja}%
kombiniranja \gls{uint}-ja s igranjem videoigara, a ima dvije krajnosti u postizanju pobjede i pružanju iskustva.

Uz navedene dvije dimenzije, autori u \cite{yannakakis2025ArtificialIntelligenceGames} navode još nekoliko faktora koje je moguće uzeti u obzir prilikom razmatranja korištenja \gls{uint}-ja u igranju videoigara. Ti faktori, koje možemo smatrati dodatnim dimenzijama koje opisuju korištenje \gls{uint}-ja u igranju igara, uključuju:

\begin{itemize}
    \item broj igrača
    \item razinu determinizma igre
    \item razinu u kojoj je igra vidljiva (engl. \emph{observability})
    \item razinu grananja unutar igre
    \item granularnost vremena igre
    \item način obrade unosa
    \item model zaključivanja
    \item oblik upravljanja modelom \gls{uint}-ja
    \item broj igara čijem igranju je namijenjen modelirani umjetni igrač.
\end{itemize}

Kombinacijom navedenih dimenzija i faktora, moguće je definirati različite vrste \gls{uint}-ja u igranju videoigara, kao što su \gls{npc}-i, igrači, treniranje igrača, treniranje \gls{npc}-a i slično.



\subsection{Igrač koji želi pobijediti}
\label{sec: igrač koji želi pobijediti}

Neki od najranijih popularnih primjera korištenja \gls{uint}-ja u igranju videoigara su umjetni igrači koji su dizajnirani da pobijede u igri. Ovi umjetni igrači mogu biti programirani da koriste različite strategije i tehnike kako bi pobijedili, a često se koriste u natjecateljskim igrama poput šaha, pokera i drugih strateških igara. U ovom kontekstu, \gls{uint} se koristi za modeliranje ponašanja igrača koji želi pobijediti. Najčešće su ti igrači modelirani kako bi pokazali ili dokazali uspješnost određenog algoritma ili tehnike, a ne nužno da bi pružili zabavu ili iskustvo igračima.

Primjeri navedenog uključuju, između ostalog:
\marginnote{najpoznatiji umjetni igrači}%
\lookat{\cref{fig: najpoznatiji umjetni igrači}}%

\begin{itemize}
    \item IBM-ov
    \marginnote{Deep Blue}%
    umjetni igrač Deep Blue \cite{campbell2002DeepBlue,hsu1995DeepBlueSystem}, koji je pobijedio Garryja Kasparova u šahu 1997. godine;
    
    \item IBM-ov Watson
    \marginnote{Watson}%
    \cite{ferrucci2010BuildingWatsonOverview}, umjetni igrač koji je pobijedio najbolje igrače u kvizu Jeopardy!;
    
    \item AlphaGo
    \marginnote{AlphaGo}%
    \cite{silver2016MasteringGameGo}, umjetni igrač Googleovog DeepMinda koji je pobijedio vrhunske igrače (Lee Sedol i Ke Jie) u igri Go 2016. godine;
    
    \item OpenAI Five
    \marginnote{OpenAI Five}%
    \cite{brockman2018OpenAIFive}, umjetni igrač koji je pobijedio profesionalne igrače u igri Dota 2 2018. godine;
    
    \item DeepMindov AlphaStar
    \marginnote{AlphaStar}%
    \cite{vinyals2017StarCraftIINew,vinyals2019GrandmasterLevelStarCraft}, umjetni igrač koji je pobijedio profesionalne igrače u igri StarCraft II.
\end{itemize}

\begin{figure}
    \centering
    \includegraphics[width=\textwidth]{Grafike/najpoznatiji umjetni igrači.pdf}
    \caption{Najpoznatiji umjetni igrači, prema \cite{yannakakis2025ArtificialIntelligenceGames}}
    \label{fig: najpoznatiji umjetni igrači}
\end{figure}

Iz popisa navedenih primjera, može se primijetiti da su se umjetni igrači učestalo koristili za igranje igara koje su determinističke, tj. igre u kojima su svi mogući ishodi poznati unaprijed, kao što su šah ili Go. Međutim, u novije vrijeme, umjetni igrači se koriste i u igrama koje su nedeterminističke, kao što su StarCraft II ili Dota 2, gdje su ishodi igre nepredvidivi i ovise o strategiji i vještini igrača.

Navedene situacije korištene su za prikaz naprednih tehnika i algoritama u području \gls{uint}-ja. Naime, videoigre su prepoznate kao izazovno okruženje za razvoj i testiranje \gls{uint}-ja, jer zahtijevaju brzo donošenje odluka, strategijsko razmišljanje i prilagodbu različitim situacijama. Ukratko, uspješno igranje igre zahtijeva određeni stupanj inteligencije, tj. igre su učestalo stvorene s ciljem da budu izazovne i zahtjevne za igrače, što ih čini idealnim poligonima za testiranje i razvoj \gls{uint}-ja.
\lookat{\cref{ex: umjetni igrači u igrama}}%
Ovdje je bitno napomenuti i da je jedan od osnovnih izvora zabave prilikom igranja igre upoznavanje s igrom u procesu igranja \cite{koster2014TheoryFunGame,yannakakis2025ArtificialIntelligenceGames}, što je dodatno pojačano razvijenim sustavom izazova koji rastu u težini, a za čije rješavanje igrači dobivaju određenu povratnu informaciju i osjećaj uspjeha. Slijedno, dobro dizajnirane videoigre pružaju svojim igračima priliku za učenje, napredovanje i usavršavanje svojih vještina, što dodatno povećava njihovu zabavnost.

\example[label=ex: umjetni igrači u igrama]
{
    U igri Civilisation VI, ljudski igrač može igrati protiv drugih ljudskih igrača ili protiv umjetnih igrača koji su programirani da koriste različite strategije i tehnike kako bi pobijedili. Ovi umjetni igrači mogu biti programirani da se prilagođavaju ponašanju igrača, koriste različite strategije i taktike te čak i surađuju s igračem kako bi pobijedili u igri. Na taj način, umjetni igrači pomažu stvaranju izazovnijeg okruženja za igrače.
}{
    Razmatranje primjera umjetnog igrača u igri Civilisation VI
}

Osim navedenog cilja testiranja naprednih algoritama i tehnika \gls{uint}-ja, modeliranje i implementacija umjetnih igrača koji žele pobijediti u igri može biti korisno i za razne oblike testiranja videoigara. Naime, umjetni igrači mogu se koristiti za testiranje balansa igre, otkrivanje grešaka i problema u dizajnu igre te za analizu različitih strategija i taktika koje igrači mogu koristiti. Ovaj pristup može pomoći u poboljšanju kvalitete igre i pružanju boljeg iskustva igračima. Ovakav način testiranja videoigara smanjuje vrijeme potrebno za testiranje te povezane troškove i omogućuje brže otkrivanje i ispravljanje grešaka, što je posebno važno u industriji video igara.

U današnje je vrijeme iznimno česta aplikacijska domena, tj. domena primjene \gls{uint}-ja u igranju video igara, treniranje modela \gls{uint}-ja korištenjem algoritama poticanog učenja (engl. \emph{reinforcement learning}) \cite{yannakakis2025ArtificialIntelligenceGames}, gdje se model trenira da igra protiv sebe samog ili protiv drugih umjetnih igrača. Ovaj pristup omogućuje modelu da uči iz vlastitih iskustava i poboljšava svoje performanse tijekom vremena, što može rezultirati stvaranjem vrlo kompetentnih umjetnih igrača. U posljednjih nekoliko godina u ovom kontekstu govorimo i o dubokom učenju (engl. \emph{deep learning}).

Važno je ovdje napomenuti da su svi navedeni primjeri umjetnih igrača osmišljeni, modelirani i implementirani s ciljem da pobijete u točno određenoj videoigri. To znači da su ti umjetni igrači trenirani i optimizirani za specifične igre, a njihova sposobnost igranja ovisi o pravilima, mehanici i strategijama koje su specifične za te igre. Ovaj pristup omogućuje stvaranje vrlo kompetentnih umjetnih igrača koji mogu pobijediti u određenim igrama, ali ne nužno i u drugim igrama ili situacijama. Drugim riječima, stvaranje umjetnog igrača koji bi mogao pobijediti u bilo kojoj igri zahtijeva mnogo širi pristup i razvoj, koji uključuje stvaranje modela umjetne opće inteligencije (engl. \emph{\gls{agi}})  \cite{yannakakis2025ArtificialIntelligenceGames}, a ne samo specifičnog modela za jednu igru \cite{swiechowski2015RecentAdvancesGeneral}. Stvaranje modela opće inteligencije iznimno je složen zadatak koji zahtijeva napredne tehnike i pristupe u području \gls{uint}-ja, a trenutno je još uvijek predmet istraživanja i razvoja.



\subsection{Neigrajući lik koji želi pobijediti}
\label{sec: neigrajući lik koji želi pobijediti}

Neigrajući likovi (engl. \emph{\acrfull{npc}}), su likovi u videoigrama koji nisu pod kontrolom igrača, već su programirani da se ponašaju na određeni način \cite{schell2019ArtGameDesign}. U kontekstu korištenja \gls{uint}-ja u igranju, neigrajući likovi mogu biti modelirani da žele pobijediti u igri, slično kao i umjetni igrači \cite{yannakakis2025ArtificialIntelligenceGames}. Ovi neigrajući likovi mogu biti programirani da koriste različite strategije i tehnike kako bi pobijedili, a često se koriste u natjecateljskim igrama ili u igrama koje zahtijevaju suradnju između igrača i neigrajućih likova.

S obzirom na to da je u osnovi svrha video igara zabava, neigrajući likovi koji žele pobijediti u igri mogu biti dizajnirani da pruže izazov igračima i učine igru zanimljivijom. Ovi neigrajući likovi mogu biti programirani da se prilagođavaju ponašanju igrača, koriste različite strategije i taktike te čak i surađuju s igračem kako bi pobijedili u igri. Na taj način, neigrajući likovi mogu doprinijeti stvaranju dinamičnijeg i izazovnijeg okruženja za igrače.

Unatoč činjenici da je cilj razvoja umjetnog igrača optimizacija njegovog ponašanja i strategija koje koristi prilikom igranja, a s ciljem bržeg i učinkovitijeg pobjeđivanja u igri, neigrajući likovi koji žele pobijediti u igri ne smiju biti savršeni i beskonačno bolji od igrača,
\lookat{\cref{ex: neigrajući lik koji želi pobijediti}}%
jer bi to moglo imati negativan utjecaj na iskustvo igrača. Naime, ako su neigrajući likovi previše jaki i uvijek pobjeđuju, igrači bi mogli izgubiti interes za igru i prestati igrati. Stoga je važno da neigrajući likovi koji žele pobijediti u igri budu dizajnirani na način koji ih čini izazovnima, ali ne i nepobjedivima.

Jedan od primjera u ovom kontekstu je modeliranje suparnika ili neprijateljskih likova u raznim igrama, a posebice u onim iz žanra pucačina (npr. \emph{first-person shooter} ili \emph{third-person shooter}) ili borbenih igara. Ovi neigrajući likovi mogu biti implementirani da koriste različite strategije i taktike kako bi pobijedili igrača. U igrama određenih žanrova, poput trkaćih igara, korištenje takvih likova je uvjet za stvaranje osnovnih obilježja žanra, kao što su izazov i konkurencija.

\example[label=ex: neigrajući lik koji želi pobijediti]
{
    U igrama poput Dark Souls ili Black Myth: Wukong, neprijateljski neigrajući likovi su programirani da koriste različite strategije i taktike kako bi pobijedili igrača. Ovi likovi mogu biti vrlo izazovni i zahtijevaju od igrača da razviju svoje vještine i strategije kako bi ih pobijedili. Na taj način, neigrajući likovi doprinose stvaranju dinamičnijeg i izazovnijeg okruženja za igrače.
}{
    Razmatranje primjera neigrajućih likova u igrama Dark Souls i Black Myth: Wukong
}



\subsection{Igrač koji igra za iskustvo}
\label{sec: igrač koji igra za iskustvo}

Ovaj oblik umjetnog igrača u svojoj se osnovi razlikuje od prethodna dva, jer njegov cilj nije pobijediti u igri. Ovakvo ponašanje može biti korisno za modeliranje i simulaciju ljudskog ponašanja, tj. za implementaciju umjetnog agenta (igrača) koji je po određenim karakteristikama sličan ljudskom igraču. Ovaj pristup može biti koristan za npr. simulacijsko testiranje igara ili u demonstracijske svrhe \cite{yannakakis2025ArtificialIntelligenceGames}.

Modeliranje umjetnog igrača koji se ponaša slično ljudskom igraču korisno je i za obogaćivanje iskustva igranja. Naime, umjetni igrači koji se ponašaju slično ljudskim igračima mogu doprinijeti stvaranju dinamičnijeg i realističnijeg okruženja za igrače. Primjerice, igre koje se temelje na timskom radu mogu imati koristi od umjetnih igrača koji surađuju s ljudskim igračima kako bi postigli zajednički cilj \cite{yannakakis2025ArtificialIntelligenceGames}. Često je navedenu kombinaciju moguće susresti u igrama koje zahtijevaju suradnju članova unutar određenog tima, poput igara popularnog žanra \emph{\gls{moba}} ili taktičkih \emph{\gls{fps}} igara. U tim igrama, umjetni igrači mogu biti programirani da se ponašaju slično ljudskim igračima, kako bi, ponekad i po potrebi, mogli donekle zamijeniti nedostatak ljudskih igrača.
\lookat{\cref{ex: igrač koji igra za iskustvo}}%

\example[label=ex: igrač koji igra za iskustvo]
{
    U igri Overwatch, umjetni igrači mogu biti programirani da se ponašaju slično ljudskim igračima. Ovi umjetni igrači mogu surađivati s ljudskim igračima kako bi postigli zajednički cilj, što može poboljšati iskustvo igranja.
}{
    Razmatranje primjera umjetnih igrača u igri Overwatch
}

Oponašanje ponašanja ljudskog igrača
\lookat{\cref{fig: oponašanje ponašanja ljudskog igrača}}%
koristi se i npr. za ponavljanje akcija ljudskog igrača i stvaranje preslike ponašanja igrača u određenoj situaciji (engl. \emph{ghost}), zatim stvaranje igrača koji se ponašaju u skladu s nekom od ustaljenih persona (engl. \emph{persona}) ili arhetipova igrača (engl. \emph{archetype}) \cite{yannakakis2025ArtificialIntelligenceGames}, kao i za stvaranje umjetnih igrača koji se ponašaju slično određenim stilovima igranja (engl. \emph{playstyle}) te za proceduralno generiranje persona ili arhetipova igrača, što u ovom kontekstu označava modificiranje ponašanja umjetnog igrača kako bi se prilagodio određenom stilu igranja ljudskog igrača.

\begin{figure}[t]
    \centering
    \includegraphics[width=\textwidth]{Grafike/bot acting human.pdf}
    \caption{Oponašanje ponašanja ljudskog igrača, prema \cite{yannakakis2025ArtificialIntelligenceGames}}
    \label{fig: oponašanje ponašanja ljudskog igrača}
\end{figure}

U kontekstu testiranja videoigara, ovaj se tip umjetnog igrača koristi za automatizaciju i ubrzavanje često iznimno repetitivnog procesa testiranja iskustva igranja određene videoigre. Naime, umjetni igrači koji se ponašaju slično ljudskim igračima mogu se koristiti za testiranje različitih aspekata igre, kao što su ravnoteža igre, mehanika igre, interakcija između igrača i neigrajućih likova te opći osjećaj igre (engl. \emph{game feel}) \cite{yannakakis2025ArtificialIntelligenceGames,swink2009GameFeelGame}. Ovaj pristup može pomoći u otkrivanju grešaka i problema u dizajnu igre te u poboljšanju kvalitete igre. Testiranje igre u ovom kontekstu 
\lookat{\cref{fig: testiranje igre umjetnim igračem}}%
može se promatrati na nekoliko razina \cite{yannakakis2025ArtificialIntelligenceGames}: od regresijskog testiranja
\marginnote{regresijsko testiranje}%
(engl. \emph{regression testing}) koje opetovano testira isti niz akcija te provjerava hoće li njihov ishod biti isti, preko sličnih ponavljanja akcija
\marginnote{slično ponavljanje akcija}%
(engl. \emph{approximate replay}) koje provjerava hoće li niz akcija postići isti ishod čak i uz sitne promjene u okolini ili akcijama, do procjene i utvrđivanja kvalitete generiranih resursa igre,
\marginnote{kvaliteta generiranih resursa}%
zatim testiranja prohodnosti, funkcionalnosti i igrivosti razina
\marginnote{testiranje razina igre}%
(engl. \emph{level}) igre ili njihovih specifičnih dijelova te, konačno, testiranja cijele igre.
\marginnote{testiranje cijele igre}%
Jasno je da je zadnja razina, općenito testiranje igre, najkompleksnija i najzahtjevnija, a ujedno i ona koja zahtijeva najviše vremena i resursa. U tom kontekstu, korištenje umjetnih igrača koji se ponašaju slično ljudskim igračima može značajno ubrzati proces testiranja i smanjiti troškove testiranja.

Ovdje je vrijedno prisjetiti se zaključka sekcije \ref{sec: igrač koji želi pobijediti}, koji ukratko opisuje kompleksnost implementacije modela umjetne opće inteligencije, a koji bi bio koristan za stvaranje umjetnog igrača koji bi mogao igrati bilo koju igru u njenoj cijelosti.

\begin{figure}[t]
    \centering
    \includegraphics[width=\textwidth]{Grafike/game testing.pdf}
    \caption{Razine testiranja igre umjetnim igračem koji igra za iskustvo, prema \cite{yannakakis2025ArtificialIntelligenceGames}}
    \label{fig: testiranje igre umjetnim igračem}
\end{figure}



\subsection{Neigrajući lik koji igra za iskustvo}
\label{sec: neigrajući lik koji igra za iskustvo}

Iako u dijelovima zvuči slično modeliranju igrača koji igra za iskustvo, neigrajući likovi koji igraju za iskustvo imaju drugačiji cilj i svrhu. Ovi neigrajući likovi mogu biti programirani da se ponašaju slično ljudskim igračima, ali njihov cilj nije pobijediti u igri, već pružiti iskustvo igračima. Ovaj pristup može biti koristan za stvaranje dinamičnijeg i realističnijeg okruženja za igrače, gdje neigrajući likovi mogu reagirati na ponašanje igrača i prilagoditi se situaciji u igri. Najčešće je ulogu neigrajućih likova u vidoigrama moguće pronaći u sljedećem neiscrpnom skupu primjera \cite{yannakakis2025ArtificialIntelligenceGames,treanor2015AIbasedGameDesign}:

\begin{itemize}
    \item likovi koji se ponašaju neprijateljski;
    \item likovi koji se ponašaju prijateljski
    \lookat{\cref{ex: neigrajući lik koji igra za iskustvo}}%
    te pružaju podršku ili vode igrača kroz svijet igre ili neki njegov segment;
    \item likovi koji su na određeni način dio pripremljene zagonetke;
    \item likovi čija je jedina zadaća sudjelovanje u pričanju priče;
    \item likovi koji imaju ulogu statista, tj. koji su prisutni u igri, ali nemaju aktivnu ulogu u interakciji s igračem.
\end{itemize}

\example[label=ex: neigrajući lik koji igra za iskustvo]
{
    U igri The Legend of Zelda: Breath of the Wild, neigrajući likovi poput vodiča ili pomagača pružaju podršku igraču, vode ga kroz svijet igre i pomažu mu u rješavanju zagonetki. Na taj način, neigrajući likovi doprinose stvaranju dinamičnijeg i realističnijeg okruženja i iskustva za igrače.
}{
    Razmatranje primjera neigrajućih likova u igri The Legend of Zelda: Breath of the Wild
}

Iako igrači očekuju u modernim igrama susresti inteligentne likove, u većini je slučajeva dovoljno da ti likovi \emph{djeluju} inteligentno, tj. da se ponašaju na način koji je uvjerljiv i realističan, čak i ako nisu uistinu inteligentni. Štoviše, implementiranje potpuno inteligentnog neigrajućeg lika, koji je dizajniran s ciljem da pobijedi, može dovesti do negativnih posljedica za iskustvo igrača, jer bi takav lik mogao biti previše jak i nepobjediv, što bi moglo frustrirati igrače i smanjiti njihovu zabavu. Stoga je važno da neigrajući likovi koji igraju za iskustvo budu dizajnirani na način koji ih čini uvjerljivima i zanimljivima, potencijalno i izazovnima, ali ne i nepobjedivima.

Uobičajeno je ovim oblikom umjetnog igrača modelirati neprijateljske neigrajuće likove, kojima nije cilj pobijediti igrača, već pružiti mu izazovno iskustvo igranja. S druge strane, modeliranje neigrajućih likova koji su namijenjeni suradnji s igračem, poput vodiča ili pomagača, uključuje optimizaciju lika s ciljem pružanja podrške igraču, a ne nužno i pobjeđivanja u igri ili igranja igre umjesto ljudskog igrača. S obzirom na niz sličnosti, nekoliko ključnih razlika te činjenicu da su suradnički neigrajući likovi nerijetko uz ljudskog igrača kroz veći dio igre od neprijateljskih likova, modeliranje njihovog ponašanja može biti zahtjevnije i izazovnije. Općenito, ponašanje takvih likova često je implementirano kombinacijom metoda koje možemo smatrati dijelom područja \gls{uint}-ja, poput konačnih automata (engl. \emph{finite state machines}) ili stabla ponašanja (engl. \emph{behaviour trees}), s algoritmima za pronalaženje puta, poput A* ili Dijkstra \cite{yannakakis2025ArtificialIntelligenceGames}.



\clearpage


\section{Metode za igranje igara}



\subsection{Modeliranje ponašanja}

\begin{itemize}
    \item klasično pretraživanje stabla stanja
    \item Monte Carlo pretraživanje
    \item informirano pretraživanje, npr. A*
    \item neinformirano pretraživanje, npr. Dijkstra, prvo u širinu
\end{itemize}



\subsection{Poticano učenje}

\begin{itemize}
    \item Q-učenje
    \item SARSA
    \item DQN
    \item PPO
    \item A3C
\end{itemize}



\subsection{Nadzirano učenje}

\begin{itemize}
    \item Generative Adversarial Imitation Learning (GAIL)
    \item Behavioural Cloning (BC)
\end{itemize}



\subsection{Veliki predtrenirani modeli}



\subsection{Kombinirane metode}