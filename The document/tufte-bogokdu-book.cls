\NeedsTeXFormat{LaTeX2e}
\ProvidesClass{tufte-bogokdu-book}[MAGO Book Document Class]



% Base class
\LoadClass[a4paper,notoc,nobib,british]{tufte-book}

\RequirePackage{geometry}
\geometry{asymmetric}

% Required packages
% \usepackage{savesym}

% \RequirePackage{aurical}
\RequirePackage[T1]{fontenc}
\RequirePackage[croatian]{babel}

% \RequirePackage{fontspec}
\RequirePackage{fontawesome}
\RequirePackage{csvsimple}
\RequirePackage{xifthen}
\RequirePackage{xparse}
\RequirePackage{xspace}
\RequirePackage{pgffor}
\RequirePackage{hyperref}
\RequirePackage{multicol}
\RequirePackage{amsmath,amsfonts,amssymb}
\RequirePackage{mathtools}
\RequirePackage{wasysym}
\RequirePackage{caption}
\RequirePackage{booktabs}
\RequirePackage{longtable}
\RequirePackage{array}
\RequirePackage{diagbox}
\RequirePackage{placeins}
\RequirePackage{qrcode}
\RequirePackage{epigraph}
\RequirePackage{imakeidx}
\RequirePackage{enotez}
\RequirePackage{amssymb}
\RequirePackage{rotating}

% Make index
\makeindex

% Use endnotes instead of footnotes
\let\footnote=\endnote

% Include preamble files
% ========================================
% BEGIN: Listings.tex
% ========================================
\usepackage{caption}
\usepackage[newfloat=true]{minted}
\setminted{
    frame=lines,
    autogobble,
    stripall,
    breaklines,
    linenos,
    numbersep=1em,
    % numberblanklines=false,
    % fontsize=\tiny,
    highlightcolor=maincolour!20,
    style=arduino,
    tabsize=2,
    escapeinside=××,
}

\newminted[mintedPython]{python}{fontsize=\tiny}
\newminted[mintedBash]{bash}{fontsize=\tiny}
\newminted[mintedXML]{xml}{fontsize=\tiny}
\newminted[mintedText]{text}{fontsize=\tiny}

\newmintedfile[mintedFilePython]{python}{fontsize=\tiny}
\newmintedfile[mintedFileJS]{js}{fontsize=\tiny}
\newmintedfile[mintedFileHTML]{html}{fontsize=\tiny}
\newmintedfile[mintedFileBash]{bash}{fontsize=\tiny}
\newmintedfile[mintedFileText]{text}{fontsize=\tiny}

\newmintedfile[mintedFilePythonBlack]{python}{fontsize=\tiny, style=monokai}

\newmintinline[mintedInline]{text}{style=bw,bgcolor=minted-inline-text, fontsize=\small}
\newmintinline[mintedInlinePython]{python}{style=bw}
\newmintinline[mintedInlineBash]{bash}{style=bw}

\SetupFloatingEnvironment{listing}{name=\prijevod{Isječak koda}{Listing}}
\SetupFloatingEnvironment{listing}{listname=\prijevod{Popis isječaka koda}{List of Listings}}

\newenvironment{longlisting}{\captionsetup{type=listing}}{}
% ========================================
% END: Listings.tex
% ========================================

% ========================================
% BEGIN: BibLaTeX.tex
% ========================================
\usepackage[date=terse, isbn=true, url=false, style=ieee, maxcitenames=3, backend=bibtex]{biblatex}
% \usepackage[date=terse, isbn=true, url=false, sorting=ydnt, citestyle=numeric, maxcitenames=3]{biblatex}
\addbibresource{lib.bib}
\usepackage[autostyle=once,english=british]{csquotes}
\renewcommand*{\mkcitation}[1]{~#1}
\renewcommand\mkblockquote[4]{\enquote{#1#2#3}#4}
% ========================================
% END: BibLaTeX.tex
% ========================================

% ========================================
% BEGIN: Colours.tex
% ========================================
\usepackage{xcolor}
\definecolor{Aero}{HTML}{06BEE1}  %
\definecolor{Alice Blue}{HTML}{E5F7FF}  %
\definecolor{Aqua}{HTML}{00FFFF}  %
\definecolor{Black Pure}{HTML}{000000}  %
\definecolor{Buff}{HTML}{EAAC8B}  %
\definecolor{Cardinal}{HTML}{CE003D}  %
\definecolor{Caribbean Current}{HTML}{1A5E63} %
\definecolor{Celadon}{HTML}{B0E298}  %
\definecolor{Celeste}{HTML}{C0FDFB}  %
\definecolor{Cerulean}{HTML}{407899}  %
\definecolor{Champagne Pink}{HTML}{EFD9CE}  %
\definecolor{Chinese Violet}{HTML}{6D597A}  %
\definecolor{Cornflower Blue}{HTML}{5887FF}  %
\definecolor{Dark Cyan}{HTML}{368F8B}  %
\definecolor{Dark Green}{HTML}{013220}  %
\definecolor{Dark Orchid}{HTML}{A833B9}  %
\definecolor{Dark Purple}{HTML}{3C022A}  %
\definecolor{Dark Slate Gray}{HTML}{255957} %
\definecolor{Dark Spring Green}{HTML}{337357} %
\definecolor{Flame}{HTML}{EE5B31}  %
\definecolor{Godot}{HTML}{478cbf}  %
\definecolor{Gunmetal}{HTML}{1D2835}  %
\definecolor{Honey Yellow}{HTML}{FFB100}  %
\definecolor{Honolulu Blue}{HTML}{0E79B2}  %
\definecolor{Hunyadi Yellow}{HTML}{F6AE2D}  %
\definecolor{Indigo Dye}{HTML}{044B7F}  %
\definecolor{Ivory}{HTML}{ECF1E4} %
\definecolor{Jet}{HTML}{2A2B2A}  %
\definecolor{Jet E}{HTML}{2E2E2E}  %
\definecolor{Lapis Lazuli}{HTML}{265A8A}  %
\definecolor{Madder}{HTML}{A31621}  %
\definecolor{Marian Blue}{HTML}{3D3B8E}  %
\definecolor{Misty Rose}{HTML}{FEE9E1}  %
\definecolor{Maya Blue}{HTML}{55C1FF}  %
\definecolor{Orange Yellow}{HTML}{E3B505}  %
\definecolor{Oxford Blue}{HTML}{001D4A}  %
\definecolor{Picton Blue}{HTML}{00A7E1}  %
\definecolor{Penn red}{HTML}{95190C}  %
\definecolor{Plum}{HTML}{8F3985}  %
\definecolor{Pomp and Power}{HTML}{73628A}  %
\definecolor{Poppy}{HTML}{DD3333}  %
\definecolor{Princeton Orange}{HTML}{FF9505} %
\definecolor{Raisin Black}{HTML}{1F1924}  %
\definecolor{Rich Black}{HTML}{131B23}  %
\definecolor{Rufous}{HTML}{A33B20}  %
\definecolor{Saffron}{HTML}{E3B505}  %
\definecolor{Sky Magenta}{HTML}{DC6BAD}  %
\definecolor{Space Cadet}{HTML}{25283D}  %
\definecolor{Tropical Indigo}{HTML}{907AD6}  %
\definecolor{Tyrian Purple}{HTML}{610345}  %
\definecolor{Verdigris}{HTML}{64B6AC}  %
\definecolor{Uranian Blue}{HTML}{C2EBFF}  %
\definecolor{White Pure}{HTML}{FFFFFF}  %
\definecolor{Xanthous}{HTML}{FBB02D}  %

\definecolor{titlered}{HTML}{58180D}
\definecolor{myblue}{HTML}{5B6C71}
\definecolor{mygreen}{HTML}{3D4635}

\colorlet{colour-primjerbox}{Caribbean Current}
\colorlet{colour-zadatakbox}{titlered}
\colorlet{colour-rjesenjebox}{titlered}
\colorlet{colour-algoritambox}{myblue}
\colorlet{colour-definicijabox}{mygreen}

\colorlet{customQuote}{Saffron}

\colorlet{alertTextColour}{Indigo Dye}

\colorlet{Colour Cover Title}{Gunmetal}
\colorlet{Colour Cover Subtitle}{Gunmetal!80}

\colorlet{minted-inline-text}{Ivory}
% ========================================
% END: Colours.tex
% ========================================

% ========================================
% BEGIN: Tikz.tex
% ========================================
\usepackage{tikz}
% \usepackage{tkz-graph}
\usepackage{tikzscale}
\usetikzlibrary{arrows, arrows.meta, mindmap, backgrounds, calc, decorations, decorations.pathmorphing, patterns, positioning, shadows, shapes, shapes.misc, shapes.geometric, shapes.symbols, fit}

% \makeatletter
% \pgfdeclaredecoration{penciline}{initial}{
%     \state{initial}[width=+\pgfdecoratedinputsegmentremainingdistance,auto corner on length=1mm,]{
%         \pgfpathcurveto%
%         {% From
%             \pgfqpoint{\pgfdecoratedinputsegmentremainingdistance}
%                             {\pgfdecorationsegmentamplitude}
%         }
%         {%  Control 1
%         \pgfmathrand
%         \pgfpointadd{\pgfqpoint{\pgfdecoratedinputsegmentremainingdistance}{0pt}}
%                         {\pgfqpoint{-\pgfdecorationsegmentaspect\pgfdecoratedinputsegmentremainingdistance}%
%                                         {\pgfmathresult\pgfdecorationsegmentamplitude}
%                         }
%         }
%         {%TO
%         \pgfpointadd{\pgfpointdecoratedinputsegmentlast}{\pgfpoint{1pt}{1pt}}
%         }
%     }
%     \state{final}{}
% }
% \makeatother

\tikzstyle{block} = [decorate, rectangle, draw, minimum height=1cm, minimum width=1cm]
\tikzstyle{free} = []
\tikzstyle{table} = [rectangle, draw, minimum height=3pt, fill=black!50, anchor = north]
\tikzset{
    hand/.pic = {
        \draw (-0.2,0) -- (1.2,0);
        \draw (-0.1,0) -- (-0.1,-0.2);
        \draw (1.1,0) -- (1.1,-0.2);
        \draw [very thick] (0.5,0) -- (0.5,0.3) -- (1.9, 0.3);
        \node (-A) at (0,0) {};
    }
}
% ========================================
% END: Tikz.tex
% ========================================

% ========================================
% BEGIN: Custom Commands.tex (Part 1)
% ========================================
\usepackage[linesnumbered]{algorithm2e}
\usepackage{newfloat}

\newcommand{\margina}[1]{
    \makebox[0pt][r]{\tiny#1\hspace*{-\linewidth}}%
}

\newcommand{\fusnota}[1]{%
    \footnote[frame]{\tiny#1}%
}

\newcommand{\customFigure}[4][1]{
    \begin{figure}
        \begin{minipage}[b]{0.7\linewidth}
            \centering
            \resizebox{#1\linewidth}{!}{#2}
        \end{minipage}
        \begin{minipage}[b]{0.05\linewidth}
        \hspace{0.1cm}
        \end{minipage}
        \begin{minipage}[b]{0.2\linewidth}
            \begin{tikzpicture}
                \node [text width = 3cm, anchor = south west, align=left] (caption) at (-0.1,-0.2){\caption{\newline#3}};
                \draw[very thick](0,-0.1)--(3,-0.1);
            \end{tikzpicture}
        \end{minipage}
        \label{fig:#4}
    \end{figure}
}

% Use example:
% \customFigure[1]{% how wide is the figure?
%     \includegraphics{path/to/the/figure.png}
% }{Caption to the figure}{figure-reference}

% this LaTeX code snippet defines a versatile colored box environment box that can be customized with different colors and titles. It also introduces two commands, \boxExample for examples and \boxTask for tasks, which simplify the creation of these boxes by predefining certain parameters like the color and title prefix, making it easier to maintain a consistent look for examples and tasks throughout a document.


\usepackage{tcolorbox}
\newtcolorbox{customBox}[3][]{
    colframe = #2!100,
    colback = #2!10,
    coltitle = #2!20,
    title = {\textbf{#3}},
    #1,
}

\newcommand{\boxExample}[2]{
    \begin{customBox}{maincolour}{\texttt{\prijevod{P.}{E.g.}}~#1}#2\end{customBox}
}
\newcommand{\boxTask}[2]{
    \begin{customBox}{taskcolour}{\texttt{\prijevod{Z.}{T.}}~#1}#2\end{customBox}
}



\newcommand{\chapterAbstract}[1]{%
    \begin{fullwidth}\noindent\textbf{\prijevod{Sažetak}{Abstract}.}\enspace#1\end{fullwidth}%\vspace{2em}
}

\newcommand{\prijevod}[2]{%
    \iflanguage{croatian}{#1}{%
    \iflanguage{british}{#2}{%
    \iflanguage{english}{#2}{}}}%
}

% O_HAI
\newcommand*\circled[1]{\tikz[baseline=(char.base)]{%
            \node[shape=circle,draw,inner sep=0.5pt] (char) {#1};}}

\newcommand{\ohai}{O\_HAI \circled{4} Games}



\newcommand{\lookat}[1]{%
    \marginnote{%
        \begin{minipage}[t]{0.1\linewidth}%
            \centering%
            \raisebox{-1pt}{\includegraphics[width=8pt]{Grafike/Common/eye.pdf}}%
        \end{minipage}%
        \prijevod{vidi}{see}~#1%
    }%
}

\newcommand{\linktext}[3][0pt]{%
    \marginnote[#1]{%
        \begin{minipage}[t]{0.1\linewidth}%
            \centering%
            \raisebox{-0.5pt}{\includegraphics[width=6pt]{Grafike/Common/link.pdf}}%
        \end{minipage}%
        \href{#2}{#3}%
    }%
}

\newcommand{\link}[2][3pt]{%
    \marginnote[#1]{%
        \begin{minipage}[t]{0.1\linewidth}%
            \centering%
            \raisebox{-1pt}{\includegraphics[width=6pt]{Grafike/Common/link.pdf}}%
        \end{minipage}%
        \tiny\url{#2}%
    }%
}

\newcommand{\lstLineNumberRef}[3][0pt]{%
    \marginnote[#1]{%
        \begin{minipage}[t]{0.1\linewidth}%
            \centering%
            \raisebox{-2pt}{\includegraphics[width=8pt]{Grafike/Common/code.png}}%
        \end{minipage}%
        \texttt{#2:#3}%
    }%
}

\newcommand{\file}[3][0pt]{\marginnote[#1]{\!\hspace{3.5pt}\raisebox{-2.5pt}{\href{#2}{\includegraphics[width=8pt]{Grafike/Common/file.png}}}\enspace\prijevod{preuzmi}{download}~\href{#2}{#3}}}

\newcommand{\video}[2][3pt]{\marginnote[#1]{\,\hspace{2pt}\raisebox{-1.5pt}{\includegraphics[width=7pt]{Grafike/Common/play}}\enspace\tiny\url{#2}}}

\newcommand{\videoNaziv}[3][3pt]{\marginnote[#1]{\,\hspace{2pt}\raisebox{-1.5pt}{\includegraphics[width=7pt]{Grafike/Common/play}}\enspace\tiny\href{#2}{#3}}}

\newcommand{\customQuoteReferenced}[2]{%
    \begin{quotation}
        \raisebox{-4pt}{\Huge "}\!\marginnote{
            \begin{minipage}[t]{0.1\linewidth}\centering$\triangleleft$\end{minipage}\MakeLowercase{\prijevod{citat iz}{cited from}} \citeauthor{#1}: \citetitle{#1} \autocite{#1}}
            % $\triangleleft$\space citat iz \citeauthor{#1}: \citetitle{#1} \autocite{#1}}
        #2
    \end{quotation}
}

\newcommand{\customQuote}[1]{\enquote{\color{customQuote}{#1}}}

% \renewcommand{\alert}[1]{\textcolor{alertTextColour}{#1}}

% \newcommand{\tocnaslovdijela}{\addvspace{20pt}\begin{center}\textsc{\prijevod{hrvatski}{english}}\end{center}\addvspace{10pt}}

% Prints the month name (e.g., January) and the year (e.g., 2008)
\newcommand{\monthyear}{%
    \prijevod{
        \ifcase\month\or siječanj\or veljača\or ožujak\or travanj\or svibanj\or lipanj\or srpanj\or kolovoz\or rujan\or listopad\or studeni\or prosinac\fi\space\number\year.
    }{
        \ifcase\month\or January\or February\or March\or April\or May\or June\or July\or August\or September\or October\or November\or December\fi\space\number\year
    }
}

\newcommand{\activityFrame}[2]{
    \begin{frame}{\insertsection}
        \begin{tikzpicture}[overlay, remember picture]
            \draw [line width=1.5em, line join=round, line cap=round]
            ([yshift=-1.77cm] current page.west) --
            ([xshift=2cm, yshift=-1.77cm] current page.west) --
            ([xshift=3.25cm, yshift=1.75cm] current page.west) --
            ([xshift=-2.25cm, yshift=-0.75cm] current page.south) --
            ([xshift=-1cm, yshift=-1.77cm] current page.center) --
            ([xshift=1.25cm, yshift=-1.77cm] current page.center);

            \node[text=frontcolour, align=left] (A)
            at ([xshift=3cm, yshift=-1.77cm] current page.center)
            {\textbf{\textsc{\huge{Activity}}}};

            \node[text=frontcolour, align=left, below = 0.3em of A.south west, anchor=north west, text width=6cm] {#1};

            \ifthenelse{\equal{#2}{}}{}{
                \node[text=frontcolour] (I)
                at ([xshift=0.5cm] A.east)
                {\href{#2}{\faExternalLink}};
            }%
        \end{tikzpicture}
    \end{frame}
}
% ========================================
% END: Custom Commands.tex
% ========================================

% ========================================
% BEGIN: Acronyms.tex
% ========================================
\usepackage[acronym, automake]{glossaries}

\makeglossaries

\setacronymstyle{long-short}

\newacronym{abm}{ABM}{agent based modelling}
\newacronym{agi}{AGI}{artificial general intelligence}
\newacronym{ai}{AI}{artificial intelligence}
\newacronym{api}{API}{application programming interface}
\newacronym{ar}{AR}{agumented reality}
\newacronym{av}{AV}{autonomous vehicle}
\newacronym{barica}{B.A.R.I.C.A.}{Barely an ARtificial Intelligence CAr}
\newacronym{barica2}{B.A.R.I.C.A.}{Beautiful ARtificial Intelligence Cognitive Agent}
\newacronym{bdi}{BDI}{belief-desire-intention}
\newacronym{diy}{DIY}{do it yourself}
\newacronym{dl}{DL}{deep learning}
\newacronym{dns}{DNS}{domain name system}
\newacronym{enisa}{ENISA}{European Union Agency for Cybersecurity}
\newacronym{esport}{eSport}{electronic sport}
\newacronym{fdms}{FDMS}{Framework for Digitally Mature Schools}
\newacronym{foi}{FOI}{Faculty of Organization and Informatics}
\newacronym{fps}{FPS}{first-person shooter}
\newacronym{fsm}{FSM}{finite state machine}
\newacronym{ftp}{FTP}{File Transfer Protocol}
\newacronym{gpa}{GPA}{grade point average}
\newacronym{gui}{GUI}{graphical user interface}
\newacronym{gt}{GT}{glossary of terms}
\newacronym{hai}{HAI}{hybrid artificial intelligence}
\newacronym{hi}{HI}{hybrid intelligence}
\newacronym{hmas}{HMAS}{holonic multiagent system}
\newacronym{ho-vo}{Ho/Vo}{holographic / volumetric}
\newacronym{http}{HTTP}{Hypertext Transfer Protocol}
\newacronym{ict}{ICT}{information communication technology}
\newacronym{if}{IF}{interactive fiction}
\newacronym{iot}{IoT}{Internet of Things}
\newacronym{ip}{IP}{Internet Protocol}
\newacronym{ive}{IVE}{intelligent virtual environment}
\newacronym{jid}{JID}{Jabber identifier}
\newacronym{json}{JSON}{javascript object notation}
\newacronym{kb}{KB}{knowledge base}
\newacronym{llm}{LLM}{large language model}
\newacronym{lsmas}{LSMAS}{large-scale multiagent system}
\newacronym{madras}{MADRaS}{Multi-Agent Autonomous Driving Simulator}
\newacronym{mago}{MAGO}{Framework for Agent Gamification Based on Ontologies}
\newacronym{mambo5}{MAMbO5}{Multiagent Model Based on Organisations for Intelligent Virtual Environments}
\newacronym{mas}{MAS}{multiagent system}
\newacronym{ml}{ML}{machine learning}
\newacronym{mmo}{MMO}{massively multi-player on-line}
\newacronym{mmorpg}{MMORPG}{massively multi-player on-line \gls{RPG}}
\newacronym{moba}{MOBA}{multiplayer online battle arena}
\newacronym{modelmmorpg}{ModelMMORPG}{Large-Scale Multi-Agent Modelling of Massively Multi-Player On-Line Role-Playing Games}
\newacronym{nlp}{NLP}{natural language processing}
\newacronym{nltk}{NLTK}{natural language toolkit}
\newacronym{npc}{NPC}{non-player character}
\newacronym{ohai}{OHAI}{Orchestration of Hybrid Artificial Intelligence Methods for Computer Games}
\newacronym{owl}{OWL}{Web Ontology Language}
\newacronym{pc}{PC}{personal computer}
\newacronym{rdf}{RDF}{resource description framework}
\newacronym{rest}{REST}{representational state transfer}
\newacronym{rpg}{RPG}{role-playing game}
\newacronym{rts}{RTS}{real-time strategy}
\newacronym{smtp}{SMTP}{simple mail transfer protocol}
\newacronym{spade}{SPADE}{Smart Python Agent Development Environment}
\newacronym{stt}{STT}{speech to text}
\newacronym{suzg}{SUZG}{Sveučilište u Zagrebu}
\newacronym{suzgfoi}{SUZGFOI}{Sveučilište u Zagrebu Fakultet organizacije i informatike}
\newacronym{ta}{TA}{teaching assistant}
\newacronym{tcp}{TCP}{Transmission Control Protocol}
\newacronym{tmw}{TMW}{The Mana World}
\newacronym{tts}{TTS}{text to speech}
\newacronym{ui}{UI}{user interface}
\newacronym{uint}{UI}{umjetna inteligencija}
\newacronym{unizg}{UNIZG}{University of Zagreb}
\newacronym{unizgfoi}{UNIZGFOI}{University of Zagreb Faculty of Organization and Informatics}
\newacronym{upv}{UPV}{Universitat Politècnica de València}
\newacronym{uri}{URI}{unique resource identifier}
\newacronym{ux}{UX}{user experience}
\newacronym{vr}{VR}{virtual reality}
\newacronym{vrain}{VRAIN}{Valencian Research Institute for Artificial Intelligence}
\newacronym{who}{WHO}{World Health Organization}
\newacronym{xmpp}{XMPP}{extensible messaging and presence protocol}
\newacronym{xml}{XML}{extensible markup language}
\newacronym{zšem}{ZŠEM}{Zagrebačka škola ekonomije i managementa}
% ========================================
% END: Acronyms.tex
% ========================================

% ========================================
% BEGIN: Whatevers Left Tufte Header.tex
% ========================================
\hypersetup{colorlinks}% uncomment this line if you prefer colored hyperlinks (e.g., for onscreen viewing)

%\usepackage{microtype}

%%
% Just some sample text
\usepackage{lipsum}

%%
% For graphics / images
\usepackage{graphicx}
\setkeys{Gin}{width=\linewidth,totalheight=\textheight,keepaspectratio}

% The fancyvrb package lets us customize the formatting of verbatim
% environments.  We use a slightly smaller font.
\usepackage{fancyvrb}
\fvset{fontsize=\normalsize}

%%
% Prints a trailing space in a smart way.
\usepackage{xspace}


% Inserts a blank page
% \newcommand{\blankpage}{\newpage\hbox{}\thispagestyle{empty}\newpage}

\usepackage{units}

% Typesets the font size, leading, and measure in the form of 10/12x26 pc.
\newcommand{\measure}[3]{#1/#2$\times$\unit[#3]{pc}}

% Macros for typesetting the documentation
\newcommand{\hlred}[1]{\textcolor{darkblue}{#1}}% prints in red

% Generates the index
% \usepackage{makeidx}
% \makeindex
% ========================================
% END: Whatevers Left Tufte Header.tex
% ========================================

% ========================================
% BEGIN: Cleveref.tex
% ========================================
\usepackage[nameinlink]{cleveref}
\crefname{figure}{\prijevod{sliku}{figure}}{\prijevod{slike}{figures}}
\Crefname{figure}{Sliku}{Slike}
\crefname{section}{\prijevod{poglavlje}{section}}{\prijevod{poglavlja}{sections}}
\Crefname{section}{Poglavlje}{Poglavlja}
\crefname{table}{\prijevod{tablicu}{table}}{\prijevod{tablice}{tables}}
\Crefname{table}{Tablica}{Tablice}
\crefname{mylisting}{
    \prijevod{izvorni kod}{listing}}{
    \prijevod{izvorne kodove}{listings}}
\Crefname{mylisting}{
    \prijevod{Izvorni kod}{Listing}}{
    \prijevod{Izvorne kodove}{Listings}}
\crefname{algo}{algoritam}{algoritme}
\Crefname{algo}{Algoritam}{Algoritme}
\crefname{mylistingE}{listing}{listings}
\Crefname{mylistingE}{Listing}{Listings}
\crefname{lstlisting}{izvorni kod}{izvorne kodove}
\Crefname{lstlisting}{Izvorni kod}{Izvorni kodovi}
\newcommand{\crefrangeconjunction}{ -- }
\newcommand{\crefpairconjunction}{\prijevod{ i }{ and }}
% ========================================
% END: Cleveref.tex
% ========================================

% ========================================
% BEGIN: Content Boxes.tex (Part 1)
% ========================================
% \usepackage[listings]{tcolorbox}
\tcbuselibrary{skins,breakable,documentation}

% \newcommand{\naslovBoxa}[2]{
%     \prijevod{#1}{#2}\marginnote{$\triangleleft$\space\MakeLowercase{\prijevod{#1}{#2}}}
% }

\newcommand{\noviBox}[3]{
    \newtcolorbox[auto counter, number within = chapter]{#1Box}[2]{
        title={
            \prijevod{#1}{#2} \thetcbcounter%
        },
        width=0.9\linewidth,
        empty, attach boxed title to top left,
        boxed title style={empty,size=minimal,toprule=3pt,top=0pt,left=5pt,
        overlay={
            \draw[#3!84,line width=1pt, solid]
            ([yshift=-2pt]frame.south west) -- ([yshift=-2pt]frame.south east);
            \node [anchor=north west, text width=\marginparwidth] at ([xshift=\linewidth+\marginparsep-3pt]frame.north west) {\begin{minipage}[t]{0.1\linewidth}\centering$\triangleleft$\end{minipage}{\prijevod{#1}{#2}}\space\thetcbcounter :\space##2};
        }},
        coltitle=#3,fonttitle=\scshape,
        before=\par\medskip\noindent,parbox=false,
        left=6pt,right=-3pt,top=4pt,
        breakable,pad at break*=0pt,vfill before first,
        overlay unbroken={
            \draw[#3!84,line width=1pt, solid]
            ([xshift=4pt,yshift=0pt]title.south west) --
            ([xshift=4pt]frame.west) --
            ([xshift=4pt,yshift=4pt]frame.south west);
            \draw[#3!84,line width=1pt, solid]
            ([yshift=4pt,xshift=5pt]frame.south east) --
            ([yshift=4pt,xshift=24pt]frame.south east) --
            ([yshift=2pt,xshift=20pt]frame.south east) --
            ([yshift=16pt,xshift=20pt]frame.south east);
        },
        overlay first={
            \draw[#3!84,line width=1pt, solid]
            ([xshift=4pt,yshift=0pt]title.south west) --
            ([xshift=4pt]frame.west) --
            ([xshift=4pt,yshift=-4pt]frame.south west);
        },
        overlay middle={
            \draw[#3!84,line width=1pt, solid]
            ([xshift=4pt,yshift=4pt]frame.north west) --
            ([xshift=4pt]frame.west) --
            ([xshift=4pt,yshift=-4pt]frame.south west);
        },
        overlay last={
            \draw[#3!84,line width=1pt, solid]
            ([xshift=4pt,yshift=4pt]frame.north west) --
            ([xshift=4pt]frame.west) --
            ([xshift=4pt,yshift=4pt]frame.south west);
            \draw[#3!84,line width=1pt, solid]
            ([yshift=4pt,xshift=5pt]frame.south east) --
            ([yshift=4pt,xshift=24pt]frame.south east) --
            ([yshift=2pt,xshift=20pt]frame.south east) --
            ([yshift=16pt,xshift=20pt]frame.south east);
        },%
        ##1
    }
}

\noviBox{Primjer}{Example}{colour-primjerbox}
\newcommand{\primjer}[3][]{
    \begin{PrimjerBox}{#1}{#3}
        #2
    \end{PrimjerBox}
}
\newcommand{\example}[3][]{\primjer[#1]{#2}{#3}}

\noviBox{Zadatak}{Exercise}{colour-zadatakbox}
\newcommand{\zadatak}[3][]{
    \begin{ZadatakBox}{#1}{#3}
        #2
    \end{ZadatakBox}
}

\noviBox{Rjesenje}{Solution}{colour-rjesenjebox}
\newcommand{\rjesenje}[3][]{
    \begin{RjesenjeBox}{#1}{#3}
        #2
    \end{RjesenjeBox}
}

\noviBox{Algoritam}{Algorithm}{colour-algoritambox}
\newcommand{\algoritam}[3][]{
    \begin{AlgoritamBox}{#1}{#3}
            #2
        \end{AlgoritamBox}
}

\noviBox{Definicija}{Definition}{colour-definicijabox}
\newcommand{\definicija}[3][]{
    \begin{DefinicijaBox}{#1}{#3}
        #2
    \end{DefinicijaBox}
}

\noviBox{Propozicija}{Proposition}{mygreen}
\newcommand{\propozicija}[3][]{
    \begin{PropozicijaBox}{#1}{#3}
        #2
    \end{PropozicijaBox}
}

\crefname{tcb@cnt@PrimjerBox}{
    \prijevod{primjer}{example}}{
    \prijevod{primjere}{examples}}
\Crefname{tcb@cnt@PrimjerBox}{
    \prijevod{Primjer}{Example}}{
    \prijevod{Primjere}{Examples}}
\crefname{tcb@cnt@ZadatakBox}{
    \prijevod{zadatak}{exercise}}{
    \prijevod{zadatke}{exercises}}
\crefname{tcb@cnt@RjesenjeBox}{
    \prijevod{rješenje}{solution}}{
    \prijevod{rješenja}{solutions}}
\crefname{tcb@cnt@AlgoritamBox}{
    \prijevod{algoritam}{algorithm}}{
    \prijevod{algoritme}{algorithms}}
\crefname{tcb@cnt@DefinicijaBox}{
    \prijevod{definicija}{definition}}{
    \prijevod{definicije}{definitions}}
% ========================================
% END: Content Boxes.tex
% ========================================

% ========================================
% BEGIN: Itemize.tex
% ========================================
\usepackage[inline]{enumitem}
\setlist[itemize,1]{leftmargin=\dimexpr 21pt, label=$\circ$}
\setlist[itemize,2]{leftmargin=\dimexpr 14pt, label=$\bullet$}
\setlist[enumerate,1]{leftmargin=\dimexpr 21pt}
\setlist[enumerate,2]{leftmargin=\dimexpr 14pt}
\setlist[description]{leftmargin=\dimexpr 21pt, labelindent=\dimexpr 21pt}
% ========================================
% END: Itemize.tex
% ========================================

% Set counters
\setcounter{secnumdepth}{2}
\setcounter{tocdepth}{2}

% Title-related commands
\makeatletter
\newcommand{\plainsubtitle}{}%     plain-text-only subtitle
\newcommand{\subtitle}[1]{%
  \gdef\@subtitle{#1}%
  \renewcommand{\plainsubtitle}{#1}% use provided plain-text title
  \ifthenelse{\isundefined{\hypersetup}}%
    {}% hyperref is not loaded; do nothing
    {\hypersetup{pdftitle={\plaintitle: \plainsubtitle{}}}}% set the PDF metadata title
}
\newcommand{\setpretitle}[1]{%
    \newcommand{\pretitle}{#1}%
}

% Custom title page
\renewcommand{\maketitlepage}[0]{%
    \cleardoublepage%
    % \AddToHook{shipout/background}{%
    %     \put (0in,-\paperheight){\includegraphics[width=\paperwidth, height=\paperheight]{Grafike/hnta_2025_home.png}}%
    % }
    {%
    \sffamily%
    \begin{fullwidth}%
        \fontsize{18}{20}\selectfont\par\noindent{\allcaps{\thanklessauthor}}%
        \vspace{11.5pc}%
        \newline%
        \begin{minipage}{\linewidth}
            \noindent
            \fontsize{10}{12}\textcolor{Colour Cover Title}{\textsc {\pretitle}}\vspace{.5em}
            
            \fontsize{30}{36}\selectfont\par\noindent{\textcolor{Colour Cover Title}{\allcaps\plaintitle}}%
        \end{minipage}%
        \newline%
        \vspace{2em}%
        \fontsize{18}{20}\selectfont\noindent\textcolor{Colour Cover Subtitle}{\textsc{\plainsubtitle}}%
        \vfill%
        \fontsize{14}{16}\selectfont\par\noindent\textsc{\thanklesspublisher}%
    \end{fullwidth}%
    }
    \thispagestyle{empty}%
    \clearpage%
    \RemoveFromHook{shipout/background}
}
\makeatother

% Main document structure
\AtBeginDocument{%
    \frontmatter
    \maketitle
    \cleardoublepage
    % ========================================
    % BEGIN: Copyright Page.tex
    % ========================================
    \cleardoublepage

    \begin{fullwidth}
    \thispagestyle{empty}
    \setlength{\parindent}{0pt}

    \prijevod{
        Ovaj dokument sastoji se od sadržaja pripremljenog za Helem Nejse Talent Akademiju održanu od 2. do 9. kolovoza 2025. godine u Sarajevu, BIH.
    }{
        This document contains the content prepared for the Helem Nejse Talent Academy held from 2 to 9 of August 2025 in Sarajevo, BIH.
    }

    \setlength{\parskip}{\baselineskip}

    \vfill

    \prijevod{Autori}{Authors}: \thanklessauthor.

    \prijevod{Urednik}{Editor}: Bogdan Okreša Đurić, \href{mailto:Bogdan Okresa Duric <<EMAIL>>}{dokresa [at] foi.unizg.hr}.

    \prijevod{
        U postupku izrade ovog dokumenta korišteni su alati i modeli generativne umjetne inteligencije.
    }{
        Tools and models of generative artificial intelligence have been used in the production of this document.
    }

    \vspace{50pt}

    \par\smallcaps{\thanklesspublisher}

    \par\href{http://ai.foi.hr}{\smallcaps{ai.foi.hr}}%

    \prijevod{
        \par Ovaj dokument licenciran je pod licencom \textit{Imenovanje-Nekomercijalno-Dijeli pod istim uvjetima 4.0 međunarodna} (CC BY-NC-SA 4.0). Potpun tekst licence dostupan je na sljedećoj adresi: \url{https://creativecommons.org/licenses/by-nc-sa/4.0/}.
        }{
        \par This work is licensed under Creative Commons \textit{Attribution-NonCommercial-ShareAlike 4.0 International} (CC BY-NC-SA 4.0). To view a copy of this license, visit \url{https://creativecommons.org/licenses/by-nc-sa/4.0/}.
    }


    \begin{center}
        \href{https://creativecommons.org/licenses/by-nc-sa/4.0/deed.hr}{\includegraphics[width=84pt]{Grafike/Common/Cc-by-nc-sa_icon.png}}
    \end{center}

    \par\textit{\prijevod{Verzija}{Version}: \monthyear}

    \prijevod{
        U slučaju da nađete greške, pozivamo vas da ih slobodno prijavite na adresu e-pošte \href{mailto:Bogdan Okresa Duric <<EMAIL>>}{dokresa [at] foi.unizg.hr}.
    }{
        In case you find any errors, do not hesitate to report them to \href{mailto:Bogdan Okresa Duric <<EMAIL>>}{dokresa [at] foi.unizg.hr}.
    }
    \end{fullwidth}
    % ========================================
    % END: Copyright Page.tex
    % ========================================
    {
    \hypersetup{linkcolor=titlered}
    \tableofcontents
    }
    \mainmatter
    \setcounter{page}{1}
}

\AtEndDocument{%
    \backmatter
    \begin{fullwidth}
        \printbibliography
        \printglossaries
        \printendnotes
        \printindex
    \end{fullwidth}
}