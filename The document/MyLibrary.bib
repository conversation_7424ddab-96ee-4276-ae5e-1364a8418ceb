@online{brockman2018OpenAIFive,
  type = {Miles<PERSON>},
  title = {{{OpenAI Five}}},
  author = {<PERSON><PERSON>, <PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON>, <PERSON> and <PERSON>\'e, <PERSON><PERSON> and <PERSON>\k <PERSON>, <PERSON><PERSON><PERSON><PERSON>\l aw and <PERSON><PERSON>, <PERSON> and <PERSON>, <PERSON><PERSON> and <PERSON><PERSON>, <PERSON> and <PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON><PERSON><PERSON> and <PERSON>, <PERSON>},
  date = {2018-06-25},
  url = {https://openai.com/index/openai-five/},
  urldate = {2025-07-27},
  abstract = {Our team of five neural networks, OpenAI Five, has started to defeat amateur human teams at Dota~2.},
  langid = {english},
  organization = {OpenAI Five}
}

@article{campbell2002DeepBlue,
  title = {Deep {{Blue}}},
  author = {<PERSON>, <PERSON> and <PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON>-<PERSON><PERSON>},
  date = {2002-01-01},
  journaltitle = {Artificial Intelligence},
  shortjournal = {Artificial Intelligence},
  volume = {134},
  number = {1},
  pages = {57--83},
  issn = {0004-3702},
  doi = {10.1016/S0004-3702(01)00129-1},
  url = {https://www.sciencedirect.com/science/article/pii/S0004370201001291},
  urldate = {2025-07-27},
  abstract = {Deep Blue is the chess machine that defeated then-reigning World Chess Champion Garry Kasparov in a six-game match in 1997. There were a number of factors that contributed to this success, including: {$\bullet$}a single-chip chess search engine,{$\bullet$}a massively parallel system with multiple levels of parallelism,{$\bullet$}a strong emphasis on search extensions,{$\bullet$}a complex evaluation function, and{$\bullet$}effective use of a Grandmaster game database. This paper describes the Deep Blue system, and gives some of the rationale that went into the design decisions behind Deep Blue.},
  keywords = {Computer chess,Evaluation function,Game tree search,Parallel search,Search extensions,Selective search}
}

@book{carse1986FiniteInfiniteGames,
  title = {Finite and {{Infinite Games}}},
  author = {Carse, James P.},
  date = {1986},
  publisher = {The Free Press},
  location = {New York, NY, USA},
  isbn = {978-0-02-905980-1},
  langid = {english},
  pagetotal = {152},
  keywords = {Games,Life,Philosophy,Religion,Symbolic aspects}
}

@book{despain2020ProfessionalTechniquesVideo,
  title = {Professional {{Techniques}} for {{Video Game Writing}}},
  editor = {Despain, Wendy},
  date = {2020},
  edition = {2},
  publisher = {Taylor \& Francis},
  location = {Boca Raton, US-FL},
  isbn = {978-0-367-18477-3},
  langid = {english}
}

@article{ferrucci2010BuildingWatsonOverview,
  title = {Building {{Watson}}: {{An Overview}} of the {{DeepQA Project}}},
  shorttitle = {Building {{Watson}}},
  author = {Ferrucci, David and Brown, Eric and Chu-Carroll, Jennifer and Fan, James and Gondek, David and Kalyanpur, Aditya A. and Lally, Adam and Murdock, J. William and Nyberg, Eric and Prager, John and Schlaefer, Nico and Welty, Chris},
  date = {2010-07-28},
  journaltitle = {AI Magazine},
  volume = {31},
  number = {3},
  pages = {59--79},
  issn = {2371-9621},
  doi = {10.1609/aimag.v31i3.2303},
  url = {https://ojs.aaai.org/aimagazine/index.php/aimagazine/article/view/2303},
  urldate = {2024-01-10},
  abstract = {IBM Research undertook a challenge to build a computer system that could compete at the human champion level in real time on the American TV Quiz show, Jeopardy! The extent of the challenge includes fielding a real-time automatic contestant on the show, not merely a laboratory exercise. The Jeopardy! Challenge helped us address requirements that led to the design of the DeepQA architecture and the implementation of Watson. After 3 years of intense research and development by a core team of about 20 researches, Watson is performing at human expert-levels in terms of precision, confidence and speed at the Jeopardy! Quiz show. Our results strongly suggest that DeepQA is an effective and extensible architecture that may be used as a foundation for combining, deploying, evaluating and advancing a wide range of algorithmic techniques to rapidly advance the field of QA.},
  issue = {3},
  langid = {english}
}

@inproceedings{hsu1995DeepBlueSystem,
  title = {Deep {{Blue System Overview}}},
  booktitle = {Proceedings of the 9th {{International Conference}} on {{Supercomputing}} - {{ICS}} '95},
  author = {Hsu, Feng-hsiung and Campbell, Murray S. and Hoane Jr., A. Joseph},
  date = {1995},
  pages = {240--244},
  publisher = {ACM Press},
  location = {Barcelona, Spain},
  doi = {10.1145/224538.224567},
  url = {http://portal.acm.org/citation.cfm?doid=224538.224567},
  urldate = {2024-07-02},
  eventtitle = {International {{Conference}} on {{Supercomputing}}},
  isbn = {978-0-89791-728-5},
  langid = {english}
}

@book{koster2014TheoryFunGame,
  title = {A {{Theory}} of {{Fun}} for {{Game Design}}},
  author = {Koster, Ralph},
  editor = {Roumeliotis, Rachel},
  date = {2014},
  edition = {2},
  publisher = {O'Reilly Media},
  location = {Sebastopol, CA, USA},
  isbn = {978-1-4493-6321-5},
  langid = {english}
}

@book{millington2019AIGames,
  title = {{{AI}} for {{Games}}},
  author = {Millington, Ian},
  date = {2019},
  edition = {3},
  publisher = {CRC Press},
  location = {Boca Raton, FL, US},
  url = {https://lccn.loc.gov/2018041305},
  isbn = {978-1-138-48397-2},
  langid = {english},
  keywords = {Artificial intelligence,Computer animation,Computer games,Programming}
}

@book{schell2019ArtGameDesign,
  title = {The {{Art}} of {{Game Design}}: {{A Book}} of {{Lenses}}},
  shorttitle = {The {{Art}} of {{Game Design}}},
  author = {Schell, Jesse},
  date = {2019},
  edition = {3},
  publisher = {Taylor \& Francis Group},
  location = {Boca Raton, US-FL},
  isbn = {978-1-138-63209-7},
  langid = {english},
  keywords = {Computer games,Design}
}

@article{silver2016MasteringGameGo,
  title = {Mastering the {{Game}} of {{Go}} with {{Deep Neural Networks}} and {{Tree Search}}},
  author = {Silver, David and Huang, Aja and Maddison, Chris J. and Guez, Arthur and Sifre, Laurent and family=Driessche, given=George, prefix=van den, useprefix=true and Schrittwieser, Julian and Antonoglou, Ioannis and Panneershelvam, Veda and Lanctot, Marc and Dieleman, Sander and Grewe, Dominik and Nham, John and Kalchbrenner, Nal and Sutskever, Ilya and Lillicrap, Timothy and Leach, Madeleine and Kavukcuoglu, Koray and Graepel, Thore and Hassabis, Demis},
  date = {2016-01},
  journaltitle = {Nature},
  volume = {529},
  number = {7587},
  pages = {484--489},
  publisher = {Nature Publishing Group},
  issn = {1476-4687},
  doi = {10.1038/nature16961},
  url = {https://www.nature.com/articles/nature16961},
  urldate = {2025-07-27},
  abstract = {The game of Go has long been viewed as the most challenging of classic games for artificial intelligence owing to its enormous search space and the difficulty of evaluating board positions and moves. Here we introduce a new approach to computer Go that uses `value networks' to evaluate board positions and `policy networks' to select moves. These deep neural networks are trained by a novel combination of supervised learning from human expert games, and reinforcement learning from games of self-play. Without any lookahead search, the neural networks play Go at the level of state-of-the-art Monte Carlo tree search programs that simulate thousands of random games of self-play. We also introduce a new search algorithm that combines Monte Carlo simulation with value and policy networks. Using this search algorithm, our program AlphaGo achieved a 99.8\% winning rate against other Go programs, and defeated the human European Go champion by 5 games to 0. This is the first time that a computer program has defeated a human professional player in the full-sized game of Go, a feat previously thought to be at least a decade away.},
  langid = {english},
  keywords = {Computational science,Computer science,Reward}
}

@article{vinyals2019GrandmasterLevelStarCraft,
  title = {Grandmaster {{Level}} in {{StarCraft II Using Multi-Agent Reinforcement Learning}}},
  author = {Vinyals, Oriol and Babuschkin, Igor and Czarnecki, Wojciech M. and Mathieu, Micha\"el and Dudzik, Andrew and Chung, Junyoung and Choi, David H. and Powell, Richard and Ewalds, Timo and Georgiev, Petko and Oh, Junhyuk and Horgan, Dan and Kroiss, Manuel and Danihelka, Ivo and Huang, Aja and Sifre, Laurent and Cai, Trevor and Agapiou, John P. and Jaderberg, Max and Vezhnevets, Alexander S. and Leblond, R\'emi and Pohlen, Tobias and Dalibard, Valentin and Budden, David and Sulsky, Yury and Molloy, James and Paine, Tom L. and Gulcehre, Caglar and Wang, Ziyu and Pfaff, Tobias and Wu, Yuhuai and Ring, Roman and Yogatama, Dani and W\"unsch, Dario and McKinney, Katrina and Smith, Oliver and Schaul, Tom and Lillicrap, Timothy and Kavukcuoglu, Koray and Hassabis, Demis and Apps, Chris and Silver, David},
  date = {2019-11},
  journaltitle = {Nature},
  volume = {575},
  number = {7782},
  pages = {350--354},
  publisher = {Nature Publishing Group},
  issn = {1476-4687},
  doi = {10.1038/s41586-019-1724-z},
  url = {https://www.nature.com/articles/s41586-019-1724-z},
  urldate = {2024-01-10},
  abstract = {Many real-world applications require artificial agents to compete and coordinate with other agents in complex environments. As a stepping stone to this goal, the domain of StarCraft has emerged as an important challenge for artificial intelligence research, owing to its iconic and enduring status among the most difficult professional esports and its relevance to the real world in terms of its raw complexity and multi-agent challenges. Over the course of a decade and numerous competitions1--3, the strongest agents have simplified important aspects of the game, utilized superhuman capabilities, or employed hand-crafted sub-systems4. Despite these advantages, no previous agent has come close to matching the overall skill of top StarCraft players. We chose to address the challenge of StarCraft using general-purpose learning methods that are in principle applicable to other complex domains: a multi-agent reinforcement learning algorithm that uses data from both human and agent games within a diverse league of continually adapting strategies and counter-strategies, each represented by deep neural networks5,6. We evaluated our agent, AlphaStar, in the full game of StarCraft II, through a series of online games against human players. AlphaStar was rated at Grandmaster level for all three StarCraft races and above 99.8\% of officially ranked human players.},
  issue = {7782},
  langid = {english},
  keywords = {Computer science,Statistics}
}

@book{yannakakis2025ArtificialIntelligenceGames,
  title = {Artificial {{Intelligence}} and {{Games}}},
  author = {Yannakakis, Georgios N. and Togelius, Julian},
  date = {2025-06-02},
  edition = {2},
  publisher = {Springer Nature Switzerland},
  location = {Cham CH},
  doi = {10.1007/978-3-031-83347-2},
  url = {https://link.springer.com/10.1007/978-3-031-83347-2},
  urldate = {2025-06-05},
  isbn = {978-3-031-83347-2},
  langid = {english},
  keywords = {Affective Computing,Artificial Intelligence (AI),Computational Intelligence (CI),Computer Games,Deep Learning,Ethics of AI and Games,Game Design,Gameplaying AI,Generative AI,Large Language Models,Machine Learning,Player Modeling,Procedural Content Generation (PCG),Reinforcement Learning,Self-supervised Learning}
}

@book{zubek2020ElementsGameDesign,
  title = {Elements of {{Game Design}}},
  author = {Zubek, Robert},
  date = {2020},
  publisher = {MIT Press},
  location = {Cambridge, MA, USA},
  url = {https://rbdigital.rbdigital.com},
  urldate = {2021-06-23},
  abstract = {An introduction to the basic concepts of game design, focusing on techniques used in commercial game production. This textbook by a well-known game designer introduces the basics of game design, covering tools and techniques used by practitioners in commercial game production. It presents a model for analyzing game design in terms of three interconnected levels--mechanics and systems, gameplay, and player experience--and explains how novice game designers can use these three levels as a framework to guide their design process. The text is notable for emphasizing models and vocabulary used in industry practice and focusing on the design of games as dynamic systems of gameplay.},
  isbn = {978-0-262-36287-0},
  langid = {english},
  annotation = {OCLC: 1159877353}
}
